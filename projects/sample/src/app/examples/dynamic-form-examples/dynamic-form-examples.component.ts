import { Component } from '@angular/core';
import {
  ACCORDION_LAYOUT_CONFIG,
  basicIconFieldExample,
  customButtonsConfig,
  HIGH_PRIORITY_FIELDS_EXAMPLE,
  MEDIUM_PRIORITY_FIELDS_EXAMPLE,
  multiSelectIconsExample,
  NEW_FIELDS_SHOWCASE,
  SECTIONS_LAYOUT_CONFIG,
  singleLayoutConfig,
  TABS_LAYOUT_CONFIG,
  WIZARD_FORM_CONFIG,
  wizardConfig,
  wizardNewConfig,
} from 'dynamic-forms';

@Component({
  selector: 'app-dynamic-form-examples',
  template: `
    <div class="min-h-screen bg-gray-50 py-12 px-4">
      <div class="max-w-3xl mx-auto space-y-12">
        <ng-container *ngFor="let config of configs">
          <lib-dynamic-form
            [config]="config"
            (formSubmit)="onSubmit($event)"
            (formChange)="onChange($event)"
            class="mb-6 border p-4 rounded shadow bg-white block"
          >
          </lib-dynamic-form>
        </ng-container>
      </div>
    </div>
  `,
})
export class DynamicFormExamplesComponent {
  public configs = [
    basicIconFieldExample,
    multiSelectIconsExample,
    // NEW_FIELDS_SHOWCASE,
    // MEDIUM_PRIORITY_FIELDS_EXAMPLE,
    // HIGH_PRIORITY_FIELDS_EXAMPLE,
    // ACCORDION_LAYOUT_CONFIG,
    // customButtonsConfig,
    // SECTIONS_LAYOUT_CONFIG,
    // singleLayoutConfig,
    // TABS_LAYOUT_CONFIG,
    // WIZARD_FORM_CONFIG,
    // wizardConfig,
    // wizardNewConfig,
  ];

  public onSubmit(formData: any): void {
    console.log('Form submitted:', formData);
  }

  public onChange(formData: any): void {
    console.log('Form changed:', formData);
  }
}
