export const fileSizeFormatter = (
  bytes: number | null | undefined,
  precision: number = 2
): string => {
  const SI_UNITS = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  if (bytes == null || isNaN(bytes) || bytes === 0) return '0 B';

  // Handle negative values
  if (bytes < 0) return `-${fileSizeFormatter(Math.abs(bytes), precision)}`;

  // Calculate size using IEC standard (1024)
  const base = 1024;
  const exponent = Math.floor(Math.log(bytes) / Math.log(base));
  const value = bytes / Math.pow(base, exponent);

  // Round to specified precision
  const roundedValue =
    Math.round(value * Math.pow(10, precision)) / Math.pow(10, precision);

  // Get unit
  const unit = SI_UNITS[Math.min(exponent, SI_UNITS.length - 1)] || 'B';

  // Format result
  return `${roundedValue.toFixed(precision).replace(/\.?0+$/, '')} ${unit}`;
};
