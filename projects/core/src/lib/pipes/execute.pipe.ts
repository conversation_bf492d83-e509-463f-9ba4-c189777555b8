import { Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'execute' })
export class ExecutePipe implements PipeTransform {
  public transform<T = any>(
    value: any,
    method: (...args: any[]) => T,
    ...args: any[]
  ): T {
    if (!this.isValidMethod(method)) return value;

    try {
      return method(value, ...args);
    } catch (error) {
      console.error('ExecutePipe error:', error);
      return value;
    }
  }

  private isValidMethod(method: any): method is Function {
    return typeof method === 'function';
  }
}
