import { IFormConfig, IIconOption } from '../interfaces';

export const singleLayoutConfig: IFormConfig = {
  id: 'user-profile',
  layout: {
    type: 'sections',
    sections: [
      {
        id: 'personal',
        title: 'Personal Information',
        rows: [
          {
            columns: [
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'firstName',
                    name: 'firstName',
                    type: 'text',
                    label: 'First Name',
                    required: true,
                    placeholder: 'Enter your first name',
                    minLength: 2,
                  },
                ],
              },
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'lastName',
                    name: 'lastName',
                    type: 'text',
                    label: 'Last Name',
                    required: true,
                    placeholder: 'Enter your last name',
                  },
                ],
              },
            ],
          },
          {
            columns: [
              {
                width: { xs: 12, md: 8 },
                fields: [
                  {
                    id: 'email',
                    name: 'email',
                    type: 'email',
                    label: 'Email Address',
                    required: true,
                    placeholder: '<EMAIL>',
                  },
                ],
              },
              {
                width: { xs: 12, md: 4 },
                fields: [
                  {
                    id: 'phone',
                    name: 'phone',
                    type: 'tel',
                    label: 'Phone Number',
                    placeholder: '+****************',
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
};

export const customButtonsConfig: IFormConfig = {
  id: 'custom-buttons-form',
  layout: {
    type: 'sections',
    sections: [
      {
        id: 'user-profile',
        title: 'User Profile',
        rows: [
          {
            columns: [
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'firstName',
                    name: 'firstName',
                    type: 'text',
                    label: 'First Name',
                    required: true,
                  },
                ],
              },
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'email',
                    name: 'email',
                    type: 'email',
                    label: 'Email',
                    required: true,
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
  buttons: {
    submit: {
      text: 'Save Profile',
      variant: 'success',
      icon: '💾',
      iconPosition: 'left',
    },
    reset: {
      text: 'Clear Form',
      variant: 'secondary',
      icon: '🔄',
    },
    cancel: {
      text: 'Cancel',
      variant: 'light',
    },
    custom: [
      {
        id: 'save-draft',
        text: 'Save as Draft',
        variant: 'info',
        icon: '📝',
        order: 1,
      },
      {
        id: 'preview',
        text: 'Preview',
        variant: 'warning',
        order: 2,
      },
    ],
  },
};

export const wizardConfig: IFormConfig = {
  id: 'registration',
  layout: {
    type: 'wizard',
    steps: [
      {
        id: 'account',
        title: 'Account Details',
        sections: [
          {
            id: 'credentials',
            rows: [
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'username',
                        name: 'username',
                        type: 'text',
                        label: 'Username',
                        required: true,
                        minLength: 3,
                        maxLength: 20,
                      },
                    ],
                  },
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'email',
                        name: 'email',
                        type: 'email',
                        label: 'Email',
                        required: true,
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'password',
                        name: 'password',
                        type: 'password',
                        label: 'Password',
                        required: true,
                        minLength: 8,
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 'preferences',
        title: 'Preferences',
        sections: [
          {
            id: 'settings',
            rows: [
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'country',
                        name: 'country',
                        type: 'select',
                        label: 'Country',
                        options: [
                          { value: 'us', label: 'United States' },
                          { value: 'ca', label: 'Canada' },
                          { value: 'uk', label: 'United Kingdom' },
                        ],
                      },
                    ],
                  },
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'notifications',
                        name: 'notifications',
                        type: 'checkbox',
                        label: 'Receive email notifications',
                        // defaultValue: true,
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
};

export const wizardNewConfig: IFormConfig = {
  id: 'wizard-form',
  layout: {
    type: 'wizard',
    steps: [
      {
        id: 'step1',
        title: 'Personal Info',
        sections: [
          {
            id: 'personal',
            rows: [
              {
                columns: [
                  {
                    width: 12,
                    fields: [
                      {
                        id: 'name',
                        name: 'name',
                        type: 'text',
                        label: 'Full Name',
                        required: true,
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
        navigationButtons: {
          next: {
            text: 'Continue',
            variant: 'primary',
          },
        },
      },
      {
        id: 'step2',
        title: 'Contact Info',
        sections: [
          {
            id: 'contact',
            rows: [
              {
                columns: [
                  {
                    width: 12,
                    fields: [
                      {
                        id: 'email',
                        name: 'email',
                        type: 'email',
                        label: 'Email',
                        required: true,
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
        navigationButtons: {
          back: {
            text: 'Previous',
            variant: 'secondary',
          },
          next: {
            text: 'Finish',
            variant: 'success',
          },
        },
      },
    ],
  },
  // No form-level buttons needed - wizard handles its own navigation
};

export const WIZARD_FORM_CONFIG: IFormConfig = {
  id: 'user-onboarding-wizard',
  version: '1.0.0',
  metadata: {
    title: 'User Onboarding Wizard',
    description: 'Complete registration process',
    author: 'Your App',
    category: 'registration',
  },
  settings: {
    showProgressBar: true,
    showStepIndicator: true,
    validateOnBlur: true,
    confirmBeforeLeave: true,
    scrollToError: true,
  },
  layout: {
    type: 'wizard',
    steps: [
      // Step 1: Personal Information
      {
        id: 'personal-info',
        title: 'Personal Information',
        subtitle: 'Tell us about yourself',
        description: 'We need some basic information to get started.',
        icon: '👤',
        sections: [
          {
            id: 'personal-details',
            title: 'Basic Details',
            rows: [
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'firstName',
                        name: 'firstName',
                        type: 'text',
                        label: 'First Name',
                        placeholder: 'Enter your first name',
                        required: true,
                        minLength: 2,
                        maxLength: 50,
                      },
                    ],
                  },
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'lastName',
                        name: 'lastName',
                        type: 'text',
                        label: 'Last Name',
                        placeholder: 'Enter your last name',
                        required: true,
                        minLength: 2,
                        maxLength: 50,
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: { xs: 12, md: 8 },
                    fields: [
                      {
                        id: 'email',
                        name: 'email',
                        type: 'email',
                        label: 'Email Address',
                        placeholder: '<EMAIL>',
                        required: true,
                        helpText: "We'll use this to contact you",
                      },
                    ],
                  },
                  {
                    width: { xs: 12, md: 4 },
                    fields: [
                      {
                        id: 'phone',
                        name: 'phone',
                        type: 'tel',
                        label: 'Phone Number',
                        placeholder: '+****************',
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
        navigationButtons: {
          next: {
            text: 'Continue',
            variant: 'primary',
            icon: '→',
            iconPosition: 'right',
          },
        },
      },

      // Step 2: Account Setup
      {
        id: 'account-setup',
        title: 'Account Setup',
        subtitle: 'Create your account',
        description: 'Choose your username and password for secure access.',
        icon: '🔐',
        sections: [
          {
            id: 'credentials',
            title: 'Login Credentials',
            rows: [
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'username',
                        name: 'username',
                        type: 'text',
                        label: 'Username',
                        placeholder: 'Choose a unique username',
                        required: true,
                        minLength: 3,
                        maxLength: 20,
                        pattern: '^[a-zA-Z0-9_]+$',
                        helpText:
                          'Only letters, numbers, and underscores allowed',
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'password',
                        name: 'password',
                        type: 'password',
                        label: 'Password',
                        placeholder: 'Enter a strong password',
                        required: true,
                        minLength: 8,
                        helpText: 'At least 8 characters required',
                      },
                    ],
                  },
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'confirmPassword',
                        name: 'confirmPassword',
                        type: 'password',
                        label: 'Confirm Password',
                        placeholder: 'Confirm your password',
                        required: true,
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
        navigationButtons: {
          back: {
            text: 'Back',
            variant: 'secondary',
            icon: '←',
            iconPosition: 'left',
          },
          next: {
            text: 'Continue',
            variant: 'primary',
            icon: '→',
            iconPosition: 'right',
          },
        },
      },

      // Step 3: Preferences (Optional/Skippable)
      {
        id: 'preferences',
        title: 'Preferences',
        subtitle: 'Customize your experience',
        description:
          'Help us personalize your experience. This step is optional.',
        icon: '⚙️',
        optional: true,
        skippable: true,
        sections: [
          {
            id: 'user-preferences',
            title: 'Your Preferences',
            rows: [
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'country',
                        name: 'country',
                        type: 'select',
                        label: 'Country',
                        placeholder: 'Select your country',
                        options: [
                          { value: 'us', label: 'United States' },
                          { value: 'ca', label: 'Canada' },
                          { value: 'uk', label: 'United Kingdom' },
                          { value: 'de', label: 'Germany' },
                          { value: 'fr', label: 'France' },
                          { value: 'au', label: 'Australia' },
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: 12,
                    fields: [
                      {
                        id: 'interests',
                        name: 'interests',
                        type: 'multiselect',
                        label: 'Interests',
                        placeholder: 'Select your interests',
                        multiple: true,
                        options: [
                          { value: 'tech', label: 'Technology' },
                          { value: 'sports', label: 'Sports' },
                          { value: 'music', label: 'Music' },
                          { value: 'travel', label: 'Travel' },
                          { value: 'cooking', label: 'Cooking' },
                          { value: 'reading', label: 'Reading' },
                          { value: 'gaming', label: 'Gaming' },
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: 12,
                    fields: [
                      {
                        id: 'notifications',
                        name: 'notifications',
                        type: 'checkbox',
                        label:
                          'Send me email notifications about updates and offers',
                        defaultValue: true,
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
        navigationButtons: {
          back: {
            text: 'Back',
            variant: 'secondary',
            icon: '←',
            iconPosition: 'left',
          },
          skip: {
            text: 'Skip for now',
            variant: 'light',
            icon: '⏭️',
            iconPosition: 'left',
          },
          next: {
            text: 'Continue',
            variant: 'primary',
            icon: '→',
            iconPosition: 'right',
          },
          custom: [
            {
              id: 'save-draft',
              text: 'Save Draft',
              variant: 'info',
              icon: '💾',
              order: 1,
            },
          ],
        },
      },

      // Step 4: Review & Confirm
      {
        id: 'review',
        title: 'Review & Confirm',
        subtitle: 'Almost done!',
        description:
          'Please review your information before completing registration.',
        icon: '📋',
        sections: [
          {
            id: 'review-section',
            title: 'Review Your Information',
            description:
              'Please verify all details are correct before submitting.',
            rows: [
              {
                columns: [
                  {
                    width: 12,
                    fields: [
                      {
                        id: 'termsAccepted',
                        name: 'termsAccepted',
                        type: 'checkbox',
                        label:
                          'I agree to the Terms of Service and Privacy Policy',
                        required: true,
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: 12,
                    fields: [
                      {
                        id: 'marketingEmails',
                        name: 'marketingEmails',
                        type: 'checkbox',
                        label: 'I want to receive marketing emails (optional)',
                        defaultValue: false,
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
        navigationButtons: {
          back: {
            text: 'Back',
            variant: 'secondary',
            icon: '←',
            iconPosition: 'left',
          },
          next: {
            text: 'Complete Registration',
            variant: 'success',
            icon: '✓',
            iconPosition: 'right',
            type: 'submit',
          },
          custom: [
            {
              id: 'print-summary',
              text: 'Print Summary',
              variant: 'light',
              icon: '🖨️',
              order: 1,
            },
          ],
        },
      },
    ],
  },

  // Form-level conditionals
  conditionals: [
    {
      id: 'password-match',
      condition: {
        field: 'confirmPassword',
        operator: 'notEquals',
        value: 'password', // This would need custom logic
      },
      actions: [
        {
          type: 'setError',
          target: 'confirmPassword',
          error: 'Passwords do not match',
        },
      ],
    },
  ],

  // Form validation settings
  validation: {
    mode: 'onBlur',
    showErrors: true,
    scrollToError: true,
    focusOnError: true,
  },

  // Hooks for custom logic
  hooks: {
    beforeStepChange: async (from, to, form) => {
      console.log(`Moving from step ${from} to ${to}`);
      // Add custom validation logic here
      return true;
    },
    onSubmit: async (values, form) => {
      console.log('Form submitted with values:', values);
      // Handle form submission
    },
  },
};

export const SECTIONS_LAYOUT_CONFIG: IFormConfig = {
  id: 'employee-profile',
  metadata: {
    title: 'Employee Profile',
    description: 'Complete employee information form',
  },
  layout: {
    type: 'sections',
    sections: [
      {
        id: 'basic-info',
        title: 'Basic Information',
        collapsible: true,
        collapsed: false,
        rows: [
          {
            columns: [
              {
                width: { xs: 12, md: 4 },
                fields: [
                  {
                    id: 'employeeId',
                    name: 'employeeId',
                    type: 'text',
                    label: 'Employee ID',
                    required: true,
                    readonly: true,
                    defaultValue: 'EMP-001',
                  },
                ],
              },
              {
                width: { xs: 12, md: 4 },
                fields: [
                  {
                    id: 'firstName',
                    name: 'firstName',
                    type: 'text',
                    label: 'First Name',
                    required: true,
                  },
                ],
              },
              {
                width: { xs: 12, md: 4 },
                fields: [
                  {
                    id: 'lastName',
                    name: 'lastName',
                    type: 'text',
                    label: 'Last Name',
                    required: true,
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 'contact-info',
        title: 'Contact Information',
        collapsible: true,
        rows: [
          {
            columns: [
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'email',
                    name: 'email',
                    type: 'email',
                    label: 'Work Email',
                    required: true,
                  },
                ],
              },
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'phone',
                    name: 'phone',
                    type: 'tel',
                    label: 'Phone Number',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 'job-details',
        title: 'Job Details',
        collapsible: true,
        rows: [
          {
            columns: [
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'department',
                    name: 'department',
                    type: 'select',
                    label: 'Department',
                    required: true,
                    options: [
                      { value: 'engineering', label: 'Engineering' },
                      { value: 'marketing', label: 'Marketing' },
                      { value: 'sales', label: 'Sales' },
                      { value: 'hr', label: 'Human Resources' },
                    ],
                  },
                ],
              },
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'position',
                    name: 'position',
                    type: 'text',
                    label: 'Job Title',
                    required: true,
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
};

export const TABS_LAYOUT_CONFIG: IFormConfig = {
  id: 'user-settings',
  metadata: {
    title: 'User Settings',
    description: 'Manage your account settings',
  },
  layout: {
    type: 'tabs',
    tabs: [
      {
        id: 'profile',
        title: 'Profile',
        icon: '👤',
        sections: [
          {
            id: 'profile-info',
            rows: [
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'displayName',
                        name: 'displayName',
                        type: 'text',
                        label: 'Display Name',
                        placeholder: 'How others see you',
                      },
                    ],
                  },
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'username',
                        name: 'username',
                        type: 'text',
                        label: 'Username',
                        required: true,
                        minLength: 3,
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: { xs: 12, md: 8 },
                    fields: [
                      {
                        id: 'email',
                        name: 'email',
                        type: 'email',
                        label: 'Email Address',
                        required: true,
                      },
                    ],
                  },
                  {
                    width: { xs: 12, md: 4 },
                    fields: [
                      {
                        id: 'website',
                        name: 'website',
                        type: 'url',
                        label: 'Website',
                        placeholder: 'https://',
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 'preferences',
        title: 'Preferences',
        icon: '⚙️',
        sections: [
          {
            id: 'user-preferences',
            rows: [
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'language',
                        name: 'language',
                        type: 'select',
                        label: 'Language',
                        defaultValue: 'en',
                        options: [
                          { value: 'en', label: 'English' },
                          { value: 'es', label: 'Spanish' },
                          { value: 'fr', label: 'French' },
                          { value: 'de', label: 'German' },
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: 12,
                    fields: [
                      {
                        id: 'notifications',
                        name: 'notifications',
                        type: 'multiselect',
                        label: 'Email Notifications',
                        multiple: true,
                        options: [
                          { value: 'updates', label: 'Product Updates' },
                          { value: 'newsletter', label: 'Newsletter' },
                          { value: 'security', label: 'Security Alerts' },
                          { value: 'marketing', label: 'Marketing' },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 'security',
        title: 'Security',
        icon: '🔒',
        badge: '!',
        sections: [
          {
            id: 'security-settings',
            rows: [
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'currentPassword',
                        name: 'currentPassword',
                        type: 'password',
                        label: 'Current Password',
                        required: true,
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'newPassword',
                        name: 'newPassword',
                        type: 'password',
                        label: 'New Password',
                        minLength: 8,
                      },
                    ],
                  },
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'confirmPassword',
                        name: 'confirmPassword',
                        type: 'password',
                        label: 'Confirm New Password',
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: 12,
                    fields: [
                      {
                        id: 'twoFactorAuth',
                        name: 'twoFactorAuth',
                        type: 'checkbox',
                        label: 'Enable Two-Factor Authentication',
                        helpText:
                          'Add an extra layer of security to your account',
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
  buttons: {
    submit: {
      text: 'Save Changes',
      variant: 'success',
    },
    reset: {
      text: 'Reset to Defaults',
      variant: 'secondary',
    },
    cancel: {
      text: 'Cancel',
      variant: 'light',
    },
  },
};

export const ACCORDION_LAYOUT_CONFIG: IFormConfig = {
  id: 'project-settings',
  metadata: {
    title: 'Project Configuration',
    description: 'Configure your project settings',
  },
  layout: {
    type: 'accordion',
    panels: [
      {
        id: 'basic-settings',
        title: 'Basic Settings',
        subtitle: 'Project name, description, and visibility',
        icon: '📋',
        expanded: true,
        sections: [
          {
            id: 'project-basic',
            rows: [
              {
                columns: [
                  {
                    width: { xs: 12, md: 8 },
                    fields: [
                      {
                        id: 'projectName',
                        name: 'projectName',
                        type: 'text',
                        label: 'Project Name',
                        required: true,
                        placeholder: 'Enter project name',
                      },
                    ],
                  },
                  {
                    width: { xs: 12, md: 4 },
                    fields: [
                      {
                        id: 'projectType',
                        name: 'projectType',
                        type: 'select',
                        label: 'Project Type',
                        required: true,
                        options: [
                          { value: 'web', label: 'Web Application' },
                          { value: 'mobile', label: 'Mobile App' },
                          { value: 'desktop', label: 'Desktop App' },
                          { value: 'api', label: 'API Service' },
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: 12,
                    fields: [
                      {
                        id: 'isPublic',
                        name: 'isPublic',
                        type: 'checkbox',
                        label: 'Make this project public',
                        helpText: 'Public projects can be viewed by anyone',
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 'team-members',
        title: 'Team Members',
        subtitle: 'Manage project collaborators',
        icon: '👥',
        sections: [
          {
            id: 'team-management',
            rows: [
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'teamLead',
                        name: 'teamLead',
                        type: 'select',
                        label: 'Team Lead',
                        required: true,
                        searchable: true,
                        options: [
                          { value: 'john_doe', label: 'John Doe' },
                          { value: 'jane_smith', label: 'Jane Smith' },
                          { value: 'mike_johnson', label: 'Mike Johnson' },
                        ],
                      },
                    ],
                  },
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'maxTeamSize',
                        name: 'maxTeamSize',
                        type: 'number',
                        label: 'Max Team Size',
                        min: 1,
                        max: 50,
                        defaultValue: 10,
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: 12,
                    fields: [
                      {
                        id: 'teamMembers',
                        name: 'teamMembers',
                        type: 'multiselect',
                        label: 'Team Members',
                        multiple: true,
                        searchable: true,
                        options: [
                          { value: 'alice_brown', label: 'Alice Brown' },
                          { value: 'bob_wilson', label: 'Bob Wilson' },
                          { value: 'carol_davis', label: 'Carol Davis' },
                          { value: 'david_miller', label: 'David Miller' },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 'advanced-config',
        title: 'Advanced Configuration',
        subtitle: 'Technical settings and integrations',
        icon: '🔧',
        sections: [
          {
            id: 'advanced-settings',
            rows: [
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'apiEndpoint',
                        name: 'apiEndpoint',
                        type: 'url',
                        label: 'API Endpoint',
                        placeholder: 'https://api.example.com',
                      },
                    ],
                  },
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'environment',
                        name: 'environment',
                        type: 'select',
                        label: 'Environment',
                        defaultValue: 'development',
                        options: [
                          { value: 'development', label: 'Development' },
                          { value: 'staging', label: 'Staging' },
                          { value: 'production', label: 'Production' },
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: 12,
                    fields: [
                      {
                        id: 'enableLogging',
                        name: 'enableLogging',
                        type: 'checkbox',
                        label: 'Enable detailed logging',
                        defaultValue: true,
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: 12,
                    fields: [
                      {
                        id: 'debugMode',
                        name: 'debugMode',
                        type: 'checkbox',
                        label: 'Enable debug mode',
                        helpText: 'Only enable in development environment',
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 'deployment',
        title: 'Deployment Settings',
        subtitle: 'Configure deployment options',
        icon: '🚀',
        sections: [
          {
            id: 'deploy-config',
            rows: [
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'deploymentStrategy',
                        name: 'deploymentStrategy',
                        type: 'select',
                        label: 'Deployment Strategy',
                        required: true,
                        options: [
                          { value: 'manual', label: 'Manual Deployment' },
                          { value: 'auto', label: 'Automatic Deployment' },
                          { value: 'scheduled', label: 'Scheduled Deployment' },
                        ],
                      },
                    ],
                  },
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'autoBackup',
                        name: 'autoBackup',
                        type: 'checkbox',
                        label: 'Enable automatic backups',
                        defaultValue: true,
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
  buttons: {
    submit: {
      text: 'Save Project Settings',
      variant: 'primary',
      icon: '💾',
    },
    custom: [
      {
        id: 'export-config',
        text: 'Export Configuration',
        variant: 'info',
        icon: '📤',
        order: 1,
      },
      {
        id: 'import-config',
        text: 'Import Configuration',
        variant: 'secondary',
        icon: '📥',
        order: 2,
      },
    ],
  },
};

export const HIGH_PRIORITY_FIELDS_EXAMPLE: IFormConfig = {
  id: 'high-priority-fields-demo',
  metadata: {
    title: 'High Priority Fields Demo',
    description: 'Showcase of textarea, radio, date, and toggle fields',
  },
  layout: {
    type: 'sections',
    sections: [
      {
        id: 'text-content',
        title: 'Text Content',
        rows: [
          {
            columns: [
              {
                width: 12,
                fields: [
                  {
                    id: 'bio',
                    name: 'bio',
                    type: 'textarea',
                    label: 'Biography',
                    placeholder: 'Tell us about yourself...',
                    rows: 4,
                    maxLength: 500,
                    showCharacterCount: true,
                    helpText: 'Share your background and interests',
                  },
                ],
              },
            ],
          },
          {
            columns: [
              {
                width: 12,
                fields: [
                  {
                    id: 'comments',
                    name: 'comments',
                    type: 'textarea',
                    label: 'Additional Comments',
                    placeholder: 'Any additional feedback...',
                    rows: 3,
                    required: false,
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 'preferences',
        title: 'Preferences',
        rows: [
          {
            columns: [
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'preferredContact',
                    name: 'preferredContact',
                    type: 'radio',
                    label: 'Preferred Contact Method',
                    required: true,
                    layout: 'vertical',
                    options: [
                      {
                        value: 'email',
                        label: 'Email',
                        description: "We'll send updates via email",
                      },
                      {
                        value: 'phone',
                        label: 'Phone',
                        description: "We'll call you for important updates",
                      },
                      {
                        value: 'sms',
                        label: 'SMS',
                        description: 'Get text message notifications',
                      },
                      {
                        value: 'none',
                        label: 'No Contact',
                        description: "Don't contact me unless urgent",
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            columns: [
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'theme',
                    name: 'theme',
                    type: 'radio',
                    label: 'Theme Preference',
                    layout: 'horizontal',
                    defaultValue: 'light',
                    options: [
                      { value: 'light', label: 'Light' },
                      { value: 'dark', label: 'Dark' },
                      { value: 'auto', label: 'Auto' },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 'dates',
        title: 'Important Dates',
        rows: [
          {
            columns: [
              {
                width: { xs: 12, md: 4 },
                fields: [
                  {
                    id: 'birthDate',
                    name: 'birthDate',
                    type: 'date',
                    label: 'Birth Date',
                    required: true,
                    dateConfig: {
                      maxDate: '2023-12-31',
                    },
                  },
                ],
              },
              {
                width: { xs: 12, md: 4 },
                fields: [
                  {
                    id: 'startDate',
                    name: 'startDate',
                    type: 'date',
                    label: 'Start Date',
                    dateConfig: {
                      minDate: '2024-01-01',
                    },
                  },
                ],
              },
              {
                width: { xs: 12, md: 4 },
                fields: [
                  {
                    id: 'meetingTime',
                    name: 'meetingTime',
                    type: 'datetime',
                    label: 'Meeting Date & Time',
                  },
                ],
              },
            ],
          },
          {
            columns: [
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'preferredTime',
                    name: 'preferredTime',
                    type: 'time',
                    label: 'Preferred Contact Time',
                  },
                ],
              },
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'targetMonth',
                    name: 'targetMonth',
                    type: 'month',
                    label: 'Target Month',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 'settings',
        title: 'Settings',
        rows: [
          {
            columns: [
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'emailNotifications',
                    name: 'emailNotifications',
                    type: 'toggle',
                    label: 'Email Notifications',
                    description: 'Receive email updates about your account',
                    defaultValue: true,
                  },
                ],
              },
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'pushNotifications',
                    name: 'pushNotifications',
                    type: 'toggle',
                    label: 'Push Notifications',
                    description: 'Get real-time notifications on your device',
                    defaultValue: false,
                  },
                ],
              },
            ],
          },
          {
            columns: [
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'darkMode',
                    name: 'darkMode',
                    type: 'toggle',
                    label: 'Dark Mode',
                    description: 'Use dark theme for better night viewing',
                  },
                ],
              },
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'autoSave',
                    name: 'autoSave',
                    type: 'toggle',
                    label: 'Auto Save',
                    description:
                      'Automatically save your work every few minutes',
                    defaultValue: true,
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
  buttons: {
    submit: {
      text: 'Save Preferences',
      variant: 'primary',
    },
    reset: {
      text: 'Reset to Defaults',
      variant: 'secondary',
    },
  },
};

export const MEDIUM_PRIORITY_FIELDS_EXAMPLE: IFormConfig = {
  id: 'medium-priority-fields-demo',
  metadata: {
    title: 'Medium Priority Fields Demo',
    description: 'Showcase of range, file, and color fields',
  },
  layout: {
    type: 'sections',
    sections: [
      {
        id: 'sliders-ranges',
        title: 'Sliders & Ranges',
        rows: [
          {
            columns: [
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'volume',
                    name: 'volume',
                    type: 'range',
                    label: 'Volume Level',
                    min: 0,
                    max: 100,
                    step: 1,
                    defaultValue: 50,
                    unit: '%',
                    showValue: true,
                  },
                ],
              },
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'price',
                    name: 'price',
                    type: 'range',
                    label: 'Price Range',
                    min: 0,
                    max: 1000,
                    step: 50,
                    defaultValue: 250,
                    unit: '$',
                    valueFormatter: (value: number) => `$${value}`,
                  },
                ],
              },
            ],
          },
          {
            columns: [
              {
                width: 12,
                fields: [
                  {
                    id: 'satisfaction',
                    name: 'satisfaction',
                    type: 'range',
                    label: 'Satisfaction Rating',
                    min: 1,
                    max: 10,
                    step: 1,
                    defaultValue: 7,
                    rangeLabels: [
                      'Very Poor',
                      'Poor',
                      'Fair',
                      'Good',
                      'Excellent',
                    ],
                    showValue: true,
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 'file-uploads',
        title: 'File Uploads',
        rows: [
          {
            columns: [
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'avatar',
                    name: 'avatar',
                    type: 'file',
                    label: 'Profile Picture',
                    fileConfig: {
                      accept: ['image/*'],
                      maxSize: 5242880, // 5MB
                      multiple: false,
                    },
                    helpText: 'Upload a profile picture (max 5MB)',
                  },
                ],
              },
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'documents',
                    name: 'documents',
                    type: 'file',
                    label: 'Supporting Documents',
                    fileConfig: {
                      accept: ['.pdf', '.doc', '.docx', '.txt'],
                      maxSize: 10485760, // 10MB
                      multiple: true,
                    },
                    helpText:
                      'Upload supporting documents (PDF, Word, Text files)',
                  },
                ],
              },
            ],
          },
          {
            columns: [
              {
                width: 12,
                fields: [
                  {
                    id: 'media',
                    name: 'media',
                    type: 'file',
                    label: 'Media Files',
                    fileConfig: {
                      accept: ['image/*', 'video/*'],
                      multiple: true,
                    },
                    helpText: 'Upload images or videos for your project',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 'colors',
        title: 'Color Selection',
        rows: [
          {
            columns: [
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'primaryColor',
                    name: 'primaryColor',
                    type: 'color',
                    label: 'Primary Color',
                    defaultValue: '#3b82f6',
                    helpText: 'Choose your brand primary color',
                  },
                ],
              },
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'secondaryColor',
                    name: 'secondaryColor',
                    type: 'color',
                    label: 'Secondary Color',
                    defaultValue: '#10b981',
                    allowFormatToggle: true,
                  },
                ],
              },
              {
                width: { xs: 12, md: 6 },
                fields: [
                  {
                    id: 'accentColor',
                    name: 'accentColor',
                    type: 'color',
                    label: 'Accent Color',
                    defaultValue: '#f59e0b',
                    presetColors: [
                      { label: 'Orange', value: '#f59e0b' },
                      { label: 'Red', value: '#ef4444' },
                      { label: 'Purple', value: '#8b5cf6' },
                      { label: 'Pink', value: '#ec4899' },
                      { label: 'Indigo', value: '#6366f1' },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
  buttons: {
    submit: {
      text: 'Save Settings',
      variant: 'primary',
    },
    reset: {
      text: 'Reset to Defaults',
      variant: 'secondary',
    },
    custom: [
      {
        id: 'preview',
        text: 'Preview Theme',
        variant: 'info',
        icon: '👁️',
      },
    ],
  },
};

export const NEW_FIELDS_SHOWCASE: IFormConfig = {
  id: 'new-fields-showcase',
  metadata: {
    title: 'New Fields Showcase',
    description: 'Demonstration of OTP and Tags field components',
  },
  layout: {
    type: 'tabs',
    tabs: [
      {
        id: 'verification',
        title: 'Verification',
        icon: '🔐',
        sections: [
          {
            id: 'otp-examples',
            title: 'OTP & PIN Fields',
            description: 'Different types of verification code inputs',
            rows: [
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'emailVerification',
                        name: 'emailVerification',
                        type: 'otp',
                        label: 'Email Verification Code',
                        helpText: 'Enter the 6-digit code sent to your email',
                        required: true,
                        otpLength: 6,
                        allowPaste: true,
                        showResend: true,
                        resendCooldown: 30,
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'smsVerification',
                        name: 'smsVerification',
                        type: 'pin',
                        label: 'SMS Verification',
                        helpText: 'Enter the 4-digit SMS code',
                        required: true,
                        otpLength: 4,
                        allowPaste: false,
                        showResend: true,
                        resendCooldown: 60,
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'backupCode',
                        name: 'backupCode',
                        type: 'otp',
                        label: 'Backup Recovery Code',
                        helpText: 'Enter your 12-digit backup code',
                        otpLength: 12,
                        allowPaste: true,
                        showResend: false,
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 'tags-and-keywords',
        title: 'Tags & Keywords',
        icon: '🏷️',
        sections: [
          {
            id: 'tags-examples',
            title: 'Tags & Chips Fields',
            description: 'Different configurations for tag input fields',
            rows: [
              {
                columns: [
                  {
                    width: 12,
                    fields: [
                      {
                        id: 'skills',
                        name: 'skills',
                        type: 'tags',
                        label: 'Technical Skills',
                        placeholder: 'Add your skills...',
                        required: true,
                        allowCustom: true,
                        maxTags: 10,
                        minChars: 2,
                        maxSuggestions: 8,
                        separators: [',', ';'],
                        suggestions: [
                          'JavaScript',
                          'TypeScript',
                          'React',
                          'Angular',
                          'Vue.js',
                          'Node.js',
                          'Python',
                          'Java',
                          'C#',
                          'PHP',
                          'Go',
                          'Rust',
                          'Docker',
                          'Kubernetes',
                          'AWS',
                          'Azure',
                          'GCP',
                          'MongoDB',
                          'PostgreSQL',
                          'MySQL',
                          'Redis',
                          'Git',
                          'CI/CD',
                          'Microservices',
                          'GraphQL',
                        ],
                        helpText:
                          'Type to search or add custom skills. Use comma or semicolon to separate.',
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'hobbies',
                        name: 'hobbies',
                        type: 'chips',
                        label: 'Hobbies & Interests',
                        placeholder: 'What do you enjoy?',
                        allowCustom: true,
                        maxTags: 5,
                        suggestions: [
                          'Reading',
                          'Photography',
                          'Traveling',
                          'Cooking',
                          'Gaming',
                          'Sports',
                          'Music',
                          'Art',
                          'Gardening',
                          'Hiking',
                          'Swimming',
                          'Cycling',
                          'Dancing',
                        ],
                        helpText: 'Select from suggestions or add your own',
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'categories',
                        name: 'categories',
                        type: 'tags',
                        label: 'Content Categories',
                        placeholder: 'Select categories...',
                        required: true,
                        allowCustom: false, // Only predefined options
                        maxTags: 3,
                        options: [
                          { value: 'tech', label: 'Technology' },
                          { value: 'business', label: 'Business' },
                          { value: 'design', label: 'Design' },
                          { value: 'marketing', label: 'Marketing' },
                          { value: 'finance', label: 'Finance' },
                          { value: 'health', label: 'Health & Wellness' },
                          { value: 'education', label: 'Education' },
                          { value: 'entertainment', label: 'Entertainment' },
                        ],
                        helpText: 'Choose up to 3 categories from the list',
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: 12,
                    fields: [
                      {
                        id: 'searchKeywords',
                        name: 'searchKeywords',
                        type: 'tags',
                        label: 'SEO Keywords',
                        placeholder: 'Enter SEO keywords...',
                        allowCustom: true,
                        maxTags: 15,
                        minChars: 1,
                        separators: [',', ';', ' '],
                        helpText:
                          'Add keywords for search engine optimization. Press space, comma, or semicolon to add.',
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 'combined-example',
        title: 'Real World Example',
        icon: '🌍',
        sections: [
          {
            id: 'user-registration',
            title: 'Complete User Registration',
            description: 'A realistic form combining multiple field types',
            rows: [
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'firstName',
                        name: 'firstName',
                        type: 'text',
                        label: 'First Name',
                        required: true,
                        placeholder: 'Enter your first name',
                      },
                    ],
                  },
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'lastName',
                        name: 'lastName',
                        type: 'text',
                        label: 'Last Name',
                        required: true,
                        placeholder: 'Enter your last name',
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: { xs: 12, md: 8 },
                    fields: [
                      {
                        id: 'email',
                        name: 'email',
                        type: 'email',
                        label: 'Email Address',
                        required: true,
                        placeholder: '<EMAIL>',
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'phoneVerification',
                        name: 'phoneVerification',
                        type: 'otp',
                        label: 'Phone Verification',
                        helpText: 'Enter the 6-digit code sent to your phone',
                        required: true,
                        otpLength: 6,
                        allowPaste: true,
                        showResend: true,
                        resendCooldown: 30,
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: 12,
                    fields: [
                      {
                        id: 'bio',
                        name: 'bio',
                        type: 'textarea',
                        label: 'About You',
                        placeholder: 'Tell us about yourself...',
                        rows: 4,
                        maxLength: 500,
                        showCharacterCount: true,
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: 12,
                    fields: [
                      {
                        id: 'expertise',
                        name: 'expertise',
                        type: 'tags',
                        label: 'Areas of Expertise',
                        placeholder: 'Add your areas of expertise...',
                        required: true,
                        allowCustom: true,
                        maxTags: 8,
                        suggestions: [
                          'Frontend Development',
                          'Backend Development',
                          'Full Stack',
                          'DevOps',
                          'Data Science',
                          'Machine Learning',
                          'AI',
                          'Project Management',
                          'Product Management',
                          'UX Design',
                          'UI Design',
                          'Quality Assurance',
                          'Security',
                          'Mobile Development',
                          'Cloud Architecture',
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'experienceLevel',
                        name: 'experienceLevel',
                        type: 'range',
                        label: 'Experience Level (Years)',
                        min: 0,
                        max: 20,
                        step: 1,
                        defaultValue: 3,
                        showValue: true,
                        unit: 'years',
                      },
                    ],
                  },
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'availability',
                        name: 'availability',
                        type: 'select',
                        label: 'Availability',
                        required: true,
                        options: [
                          { value: 'full-time', label: 'Full-time' },
                          { value: 'part-time', label: 'Part-time' },
                          { value: 'contract', label: 'Contract' },
                          { value: 'freelance', label: 'Freelance' },
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                columns: [
                  {
                    width: 12,
                    fields: [
                      {
                        id: 'agreeToTerms',
                        name: 'agreeToTerms',
                        type: 'checkbox',
                        label:
                          'I agree to the Terms of Service and Privacy Policy',
                        required: true,
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
  buttons: {
    submit: {
      text: 'Complete Registration',
      variant: 'success',
      icon: '✅',
    },
    reset: {
      text: 'Clear Form',
      variant: 'secondary',
    },
    custom: [
      {
        id: 'save-draft',
        text: 'Save as Draft',
        variant: 'info',
        icon: '💾',
        order: 1,
      },
      {
        id: 'preview',
        text: 'Preview',
        variant: 'warning',
        icon: '👁️',
        order: 2,
      },
    ],
  },
  // Conditional logic example
  conditionals: [
    {
      id: 'show-phone-verification',
      condition: { field: 'email', operator: 'notEmpty' },
      actions: [{ type: 'show', target: 'phoneVerification' }],
    },
  ],
};

// Example 1: Basic Icon Selection with FontAwesome
export const basicIconFieldExample: IFormConfig = {
  id: 'icon-selection-form',
  layout: {
    type: 'sections',
    sections: [
      {
        id: 'main',
        title: 'Select Your Icon',
        rows: [
          {
            columns: [
              {
                width: 12,
                fields: [
                  {
                    id: 'user-icon',
                    name: 'userIcon',
                    type: 'icons',
                    label: 'Choose an icon',
                    placeholder: 'Select an icon',
                    required: true,
                    iconConfig: {
                      options: [
                        {
                          value: 'user',
                          label: 'User',
                          iconClass: 'fas fa-user',
                          category: 'User',
                        },
                        {
                          value: 'user-circle',
                          label: 'User Circle',
                          iconClass: 'fas fa-user-circle',
                          category: 'User',
                        },
                        {
                          value: 'user-plus',
                          label: 'Add User',
                          iconClass: 'fas fa-user-plus',
                          category: 'User',
                        },
                        {
                          value: 'users',
                          label: 'Users',
                          iconClass: 'fas fa-users',
                          category: 'User',
                        },
                        {
                          value: 'home',
                          label: 'Home',
                          iconClass: 'fas fa-home',
                          category: 'Navigation',
                        },
                        {
                          value: 'settings',
                          label: 'Settings',
                          iconClass: 'fas fa-cog',
                          category: 'System',
                        },
                        {
                          value: 'bell',
                          label: 'Notifications',
                          iconClass: 'fas fa-bell',
                          category: 'System',
                        },
                        {
                          value: 'envelope',
                          label: 'Email',
                          iconClass: 'fas fa-envelope',
                          category: 'Communication',
                        },
                        {
                          value: 'phone',
                          label: 'Phone',
                          iconClass: 'fas fa-phone',
                          category: 'Communication',
                        },
                        {
                          value: 'comment',
                          label: 'Comment',
                          iconClass: 'fas fa-comment',
                          category: 'Communication',
                        },
                      ],
                      layout: 'grid',
                      itemsPerRow: 6,
                      size: 'large',
                    },
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
};

// Example 2: Multi-select Icons with Material Icons
export const multiSelectIconsExample: IFormConfig = {
  id: 'multi-icon-form',
  layout: {
    type: 'sections',
    sections: [
      {
        id: 'main',
        title: 'Select Multiple Features',
        rows: [
          {
            columns: [
              {
                width: 12,
                fields: [
                  {
                    id: 'features',
                    name: 'features',
                    type: 'icons',
                    label: 'Select Features (max 5)',
                    multiple: true,
                    maxSelection: 5,
                    minSelection: 1,
                    required: true,
                    iconConfig: {
                      searchable: true,
                      showSelected: true,
                      showCategories: true,
                      options: getMaterialIconOptions(),
                      layout: 'grid',
                      itemsPerRow: 8,
                      size: 'medium',
                      itemClassName: 'material-icons',
                    },
                    validation: [
                      {
                        type: 'minItems',
                        value: 1,
                        message: 'Please select at least one feature',
                      },
                      {
                        type: 'maxItems',
                        value: 5,
                        message: 'You can select maximum 5 features',
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
};

// Example 3: SVG Icons with Custom Styling
export const svgIconsExample: IFormConfig = {
  id: 'svg-icons-form',
  layout: {
    type: 'sections',
    sections: [
      {
        id: 'main',
        title: 'Select Payment Method',
        rows: [
          {
            columns: [
              {
                width: 12,
                fields: [
                  {
                    id: 'payment-method',
                    name: 'paymentMethod',
                    type: 'icons',
                    label: 'Choose Payment Method',
                    required: true,
                    iconConfig: {
                      searchable: false,
                      showCategories: false,
                      options: [
                        {
                          value: 'visa',
                          label: 'Visa',
                          svg: '<svg><!-- Visa SVG --></svg>',
                          color: '#1A1F71',
                        },
                        {
                          value: 'mastercard',
                          label: 'Mastercard',
                          svg: '<svg><!-- Mastercard SVG --></svg>',
                          color: '#EB001B',
                        },
                        {
                          value: 'paypal',
                          label: 'PayPal',
                          svg: '<svg><!-- PayPal SVG --></svg>',
                          color: '#003087',
                        },
                        {
                          value: 'stripe',
                          label: 'Stripe',
                          svg: '<svg><!-- Stripe SVG --></svg>',
                          color: '#635BFF',
                        },
                      ],
                      layout: 'grid',
                      itemsPerRow: 4,
                      size: 'large', // 'xlarge' not in your size type, mapped to 'large'
                      // Custom styling for icons container/items can be handled via className/itemClassName
                      className: 'custom-icon-style',
                      itemClassName: 'custom-icon-item-style',
                    },
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
};

// Example 4: Social Media Icons
export const socialMediaIconsExample: IFormConfig = {
  id: 'social-media-form',
  layout: {
    type: 'sections',
    sections: [
      {
        id: 'main',
        title: 'Connect Your Social Media',
        rows: [
          {
            columns: [
              {
                width: 12,
                fields: [
                  {
                    id: 'social-accounts',
                    name: 'socialAccounts',
                    type: 'icons',
                    label: 'Select Social Networks',
                    multiple: true,
                    iconConfig: {
                      searchable: true,
                      showSelected: true,
                      options: [
                        {
                          value: 'facebook',
                          label: 'Facebook',
                          iconClass: 'fab fa-facebook',
                          color: '#1877F2',
                        },
                        {
                          value: 'twitter',
                          label: 'Twitter',
                          iconClass: 'fab fa-twitter',
                          color: '#1DA1F2',
                        },
                        {
                          value: 'instagram',
                          label: 'Instagram',
                          iconClass: 'fab fa-instagram',
                          color: '#E4405F',
                        },
                        {
                          value: 'linkedin',
                          label: 'LinkedIn',
                          iconClass: 'fab fa-linkedin',
                          color: '#0A66C2',
                        },
                        {
                          value: 'youtube',
                          label: 'YouTube',
                          iconClass: 'fab fa-youtube',
                          color: '#FF0000',
                        },
                        {
                          value: 'tiktok',
                          label: 'TikTok',
                          iconClass: 'fab fa-tiktok',
                          color: '#000000',
                        },
                        {
                          value: 'pinterest',
                          label: 'Pinterest',
                          iconClass: 'fab fa-pinterest',
                          color: '#E60023',
                        },
                        {
                          value: 'snapchat',
                          label: 'Snapchat',
                          iconClass: 'fab fa-snapchat',
                          color: '#FFFC00',
                        },
                        {
                          value: 'reddit',
                          label: 'Reddit',
                          iconClass: 'fab fa-reddit',
                          color: '#FF4500',
                        },
                        {
                          value: 'whatsapp',
                          label: 'WhatsApp',
                          iconClass: 'fab fa-whatsapp',
                          color: '#25D366',
                        },
                      ],
                      layout: 'grid',
                      itemsPerRow: 5,
                      size: 'large',
                    },
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
};

// Helper function to generate Material Icons options
function getMaterialIconOptions(): IIconOption[] {
  const icons = [
    // Actions
    { value: 'search', label: 'Search', category: 'Actions' },
    { value: 'home', label: 'Home', category: 'Actions' },
    { value: 'favorite', label: 'Favorite', category: 'Actions' },
    { value: 'delete', label: 'Delete', category: 'Actions' },
    { value: 'add', label: 'Add', category: 'Actions' },
    { value: 'remove', label: 'Remove', category: 'Actions' },
    { value: 'check', label: 'Check', category: 'Actions' },
    { value: 'close', label: 'Close', category: 'Actions' },

    // Communication
    { value: 'email', label: 'Email', category: 'Communication' },
    { value: 'phone', label: 'Phone', category: 'Communication' },
    { value: 'chat', label: 'Chat', category: 'Communication' },
    { value: 'message', label: 'Message', category: 'Communication' },

    // File
    { value: 'folder', label: 'Folder', category: 'File' },
    { value: 'file_copy', label: 'File', category: 'File' },
    { value: 'cloud_upload', label: 'Upload', category: 'File' },
    { value: 'cloud_download', label: 'Download', category: 'File' },

    // Hardware
    { value: 'computer', label: 'Computer', category: 'Hardware' },
    { value: 'keyboard', label: 'Keyboard', category: 'Hardware' },
    { value: 'mouse', label: 'Mouse', category: 'Hardware' },
    { value: 'headset', label: 'Headset', category: 'Hardware' },
  ];

  return icons.map((icon) => ({
    ...icon,
    tags: [icon.value, icon.label.toLowerCase(), icon.category.toLowerCase()],
  }));
}

// Example 5: Emoji Icons
export const emojiIconsExample: IFormConfig = {
  id: 'emoji-form',
  layout: {
    type: 'sections',
    sections: [
      {
        id: 'main',
        title: 'Select Your Mood',
        rows: [
          {
            columns: [
              {
                width: 12,
                fields: [
                  {
                    id: 'mood',
                    name: 'mood',
                    type: 'icons',
                    label: 'How are you feeling?',
                    required: true,
                    iconConfig: {
                      searchable: false,
                      showCategories: false,
                      options: [
                        {
                          value: 'very-happy',
                          label: 'Very Happy',
                          emoji: '😄',
                        },
                        { value: 'happy', label: 'Happy', emoji: '😊' },
                        { value: 'neutral', label: 'Neutral', emoji: '😐' },
                        { value: 'sad', label: 'Sad', emoji: '😢' },
                        { value: 'very-sad', label: 'Very Sad', emoji: '😭' },
                      ],
                      layout: 'grid',
                      itemsPerRow: 5,
                      size: 'large', // 'xlarge' mapped to 'large'
                    },
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
};
