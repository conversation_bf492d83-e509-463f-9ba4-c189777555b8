import { APP_INITIALIZER, ModuleWithProviders, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// Directives
import { FieldHostDirective, LayoutHostDirective } from './directives';

// Services
import {
  ArrayFieldService,
  ConditionalEngineService,
  FieldRegistryService,
  FieldTypeRegistryService,
  FormBuilderService,
  FormContextService,
  FormStateService,
  IconRegistryService,
  LayoutRegistryService,
  ValidationErrorService,
  ValidatorManagerService,
  ValidatorRegistryService,
} from './services';

// Components
import { FieldWrapperComponent } from './components/field-wrapper/field-wrapper.component';

// Layout Components
import { SingleLayoutComponent } from './components/layouts/single-layout/single-layout.component';
import { WizardLayoutComponent } from './components/layouts/wizard-layout/wizard-layout.component';
import { TabsLayoutComponent } from './components/layouts/tabs-layout/tabs-layout.component';
import { AccordionLayoutComponent } from './components/layouts/accordion-layout/accordion-layout.component';

// Structural Components
import { SectionComponent } from './components/section/section.component';
import { RowComponent } from './components/row/row.component';
import { ColumnComponent } from './components/column/column.component';

// Field Components
import { TextFieldComponent } from './components/fields/text-field/text-field.component';
import { SelectFieldComponent } from './components/fields/select-field/select-field.component';
import { CheckboxFieldComponent } from './components/fields/checkbox-field/checkbox-field.component';
import { NumberFieldComponent } from './components/fields/number-field/number-field.component';
// High Priority Fields
import { TextareaFieldComponent } from './components/fields/textarea-field/textarea-field.component';
import { RadioFieldComponent } from './components/fields/radio-field/radio-field.component';
import { DateFieldComponent } from './components/fields/date-field/date-field.component';
import { ToggleFieldComponent } from './components/fields/toggle-field/toggle-field.component';
// Medium Priority Fields
import { RangeFieldComponent } from './components/fields/range-field/range-field.component';
import { FileFieldComponent } from './components/fields/file-field/file-field.component';
import { ColorFieldComponent } from './components/fields/color-field/color-field.component';
// Phase 1 Fields (Quick Wins)
import { OTPFieldComponent } from './components/fields/otp-field/otp-field.component';
import { TagsFieldComponent } from './components/fields/tags-field/tags-field.component';

// Main Form Component
import { DynamicFormComponent } from './components/dynamic-form/dynamic-form.component';
import { CoreModule } from 'core';
import { IconsFieldComponent } from './components';

const COMPONENTS = [
  // Directives
  FieldHostDirective,
  LayoutHostDirective,

  // Components
  FieldWrapperComponent,

  // Layout Components
  SingleLayoutComponent,
  WizardLayoutComponent,
  TabsLayoutComponent,
  AccordionLayoutComponent,

  // Structural Components
  SectionComponent,
  RowComponent,
  ColumnComponent,

  // Field Components
  TextFieldComponent,
  SelectFieldComponent,
  CheckboxFieldComponent,
  NumberFieldComponent,
  TextareaFieldComponent,
  RadioFieldComponent,
  DateFieldComponent,
  ToggleFieldComponent,
  RangeFieldComponent,
  FileFieldComponent,
  ColorFieldComponent,
  OTPFieldComponent,
  TagsFieldComponent,
  IconsFieldComponent,

  // Main Component
  DynamicFormComponent,
];

const SERVICES = [
  ArrayFieldService,
  ConditionalEngineService,
  FieldRegistryService,
  FieldTypeRegistryService,
  FormBuilderService,
  FormContextService,
  FormStateService,
  LayoutRegistryService,
  ValidationErrorService,
  ValidatorManagerService,
  ValidatorRegistryService,
  IconRegistryService,
];

export function initializeDynamicForms(
  fieldTypeRegistry: FieldTypeRegistryService,
  layoutRegistry: LayoutRegistryService,
  validatorRegistry: ValidatorRegistryService
): () => void {
  return () => {
    // Register basic field components
    fieldTypeRegistry.register('text', TextFieldComponent);
    fieldTypeRegistry.register('email', TextFieldComponent);
    fieldTypeRegistry.register('password', TextFieldComponent);
    fieldTypeRegistry.register('tel', TextFieldComponent);
    fieldTypeRegistry.register('url', TextFieldComponent);
    fieldTypeRegistry.register('search', TextFieldComponent);
    fieldTypeRegistry.register('select', SelectFieldComponent);
    fieldTypeRegistry.register('multiselect', SelectFieldComponent);
    fieldTypeRegistry.register('checkbox', CheckboxFieldComponent);
    fieldTypeRegistry.register('number', NumberFieldComponent);

    // Register high priority field components
    fieldTypeRegistry.register('textarea', TextareaFieldComponent);
    fieldTypeRegistry.register('radio', RadioFieldComponent);
    fieldTypeRegistry.register('date', DateFieldComponent);
    fieldTypeRegistry.register('datetime', DateFieldComponent);
    fieldTypeRegistry.register('time', DateFieldComponent);
    fieldTypeRegistry.register('month', DateFieldComponent);
    fieldTypeRegistry.register('week', DateFieldComponent);
    fieldTypeRegistry.register('toggle', ToggleFieldComponent);
    fieldTypeRegistry.register('switch', ToggleFieldComponent);

    // Register medium priority field components
    fieldTypeRegistry.register('range', RangeFieldComponent);
    fieldTypeRegistry.register('slider', RangeFieldComponent);
    fieldTypeRegistry.register('file', FileFieldComponent);
    fieldTypeRegistry.register('image', FileFieldComponent);
    fieldTypeRegistry.register('document', FileFieldComponent);
    fieldTypeRegistry.register('color', ColorFieldComponent);

    // Register Phase 1 field components
    fieldTypeRegistry.register('otp', OTPFieldComponent);
    fieldTypeRegistry.register('pin', OTPFieldComponent);
    fieldTypeRegistry.register('tags', TagsFieldComponent);
    fieldTypeRegistry.register('chips', TagsFieldComponent);

    fieldTypeRegistry.register('icons', IconsFieldComponent);

    // Register layout components
    layoutRegistry.register('single', SingleLayoutComponent);
    layoutRegistry.register('sections', SingleLayoutComponent);
    layoutRegistry.register('grid', SingleLayoutComponent);
    layoutRegistry.register('wizard', WizardLayoutComponent);
    layoutRegistry.register('tabs', TabsLayoutComponent);
    layoutRegistry.register('accordion', AccordionLayoutComponent);

    // Register default validators
    validatorRegistry.registerDefaultValidators();
  };
}

@NgModule({
  declarations: COMPONENTS,
  imports: [CommonModule, CoreModule, ReactiveFormsModule, FormsModule],
  exports: COMPONENTS,
})
export class DynamicFormsModule {
  static forRoot(): ModuleWithProviders<DynamicFormsModule> {
    return {
      ngModule: DynamicFormsModule,
      providers: [
        ...SERVICES,
        {
          provide: APP_INITIALIZER,
          useFactory: initializeDynamicForms,
          deps: [
            FieldTypeRegistryService,
            LayoutRegistryService,
            ValidatorRegistryService,
          ],
          multi: true,
        },
      ],
    };
  }
}
