// layout-host.directive.ts
import {
  Directive,
  Input,
  ViewContainerRef,
  OnInit,
  Injector,
  Type,
  ComponentFactoryResolver,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { LayoutRegistryService } from '../services/layout-registry.service';
import { IFormLayout } from '../interfaces';

@Directive({ selector: '[libLayoutHost]' })
export class LayoutHostDirective implements OnInit {
  @Input('libLayoutHost') public layout!: IFormLayout;
  @Input() public form?: FormGroup;

  public constructor(
    private readonly vcr: ViewContainerRef,
    private readonly injector: Injector,
    private readonly layoutRegistry: LayoutRegistryService,
    private readonly cfr: ComponentFactoryResolver
  ) {}

  public ngOnInit(): void {
    this.renderLayout();
  }

  private renderLayout(): void {
    this.vcr.clear();

    const type: string = this.layout?.type ?? '';
    const comp: Type<any> | undefined = this.layoutRegistry.get(type);

    if (!comp) return;

    const factory = this.cfr.resolveComponentFactory(comp);
    const ref = this.vcr.createComponent(factory, undefined, this.injector);

    ref.instance.layout = this.layout;
    ref.instance.form = this.form;
  }
}
