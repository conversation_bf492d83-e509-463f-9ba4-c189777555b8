import {
  Directive,
  Input,
  ViewContainerRef,
  OnInit,
  Injector,
  ComponentFactoryResolver,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IField } from '../interfaces';
import { FieldTypeRegistryService } from '../services';

@Directive({ selector: '[libFieldHost]' })
export class FieldHostDirective implements OnInit {
  @Input('libFieldHost') public field!: IField;
  @Input() public form!: FormGroup;

  public constructor(
    private readonly vcr: ViewContainerRef,
    private readonly injector: Injector,
    private readonly registry: FieldTypeRegistryService,
    private readonly cfr: ComponentFactoryResolver
  ) {}

  public ngOnInit(): void {
    this.renderField();
  }

  private renderField(): void {
    this.vcr.clear();

    const fieldComponent = this.registry.get(this.field.type);

    if (!fieldComponent) return;

    const factory = this.cfr.resolveComponentFactory(fieldComponent);
    const ref = this.vcr.createComponent(factory, undefined, this.injector);

    ref.instance.field = this.field;
    ref.instance.form = this.form;
  }
}
