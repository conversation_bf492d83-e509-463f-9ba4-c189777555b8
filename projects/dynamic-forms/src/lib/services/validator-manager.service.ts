import { Injectable } from '@angular/core';
import { AbstractControl, ValidatorFn, AsyncValidatorFn } from '@angular/forms';
import { Observable, of } from 'rxjs';
import { debounceTime, switchMap, catchError } from 'rxjs/operators';
import { ValidatorRegistryService } from './validator-registry.service';
import { IValidationRule, IAsyncValidationRule, IField } from '../interfaces';

interface ControlValidatorsState {
  static: ValidatorFn[];
  dynamic: Map<string, ValidatorFn>;
  async: Map<string, AsyncValidatorFn>;
}

@Injectable()
export class ValidatorManagerService {
  private readonly state = new WeakMap<
    AbstractControl,
    ControlValidatorsState
  >();

  public constructor(
    private readonly validatorRegistry: ValidatorRegistryService
  ) {}

  public build(field: IField): ValidatorFn[] {
    const allRules = [...(field.validation ?? [])];

    // Auto-derive validation rules from field properties
    this.addAutoValidation(field, allRules);

    return allRules
      .map((rule) => this.validatorRegistry.create(rule.type, rule))
      .filter((fn): fn is ValidatorFn => !!fn);
  }

  private addAutoValidation(field: IField, rules: IValidationRule[]): void {
    // Required validation
    if (field.required && !this.hasRule(rules, 'required')) {
      rules.unshift({ type: 'required' });
    }

    // Email validation (auto for email type)
    if (field.type === 'email' && !this.hasRule(rules, 'email')) {
      rules.push({ type: 'email' });
    }

    // URL validation (auto for url type)
    if (field.type === 'url' && !this.hasRule(rules, 'url')) {
      rules.push({ type: 'url' });
    }

    // MinLength validation
    if (field.minLength && !this.hasRule(rules, 'minLength')) {
      rules.push({ type: 'minLength', value: field.minLength });
    }

    // MaxLength validation
    if (field.maxLength && !this.hasRule(rules, 'maxLength')) {
      rules.push({ type: 'maxLength', value: field.maxLength });
    }

    // Pattern validation
    if (field.pattern && !this.hasRule(rules, 'pattern')) {
      rules.push({ type: 'pattern', pattern: field.pattern });
    }

    // Number field validations
    if (field.type === 'number' || field.type === 'range') {
      if (field.min !== undefined && !this.hasRule(rules, 'min')) {
        rules.push({ type: 'min', value: field.min });
      }
      if (field.max !== undefined && !this.hasRule(rules, 'max')) {
        rules.push({ type: 'max', value: field.max });
      }
    }

    // Select field validations
    if (field.type === 'multiselect') {
      if (field.minSelection && !this.hasRule(rules, 'minItems')) {
        rules.push({ type: 'minItems', value: field.minSelection });
      }
      if (field.maxSelection && !this.hasRule(rules, 'maxItems')) {
        rules.push({ type: 'maxItems', value: field.maxSelection });
      }
    }

    if (field.type === 'color' && !this.hasRule(rules, 'color')) {
      rules.push({ type: 'color' });
    }
  }

  private hasRule(rules: IValidationRule[], ruleType: string): boolean {
    return rules.some((rule) => rule.type === ruleType);
  }

  public buildAsync(rules: IAsyncValidationRule[] = []): AsyncValidatorFn[] {
    return rules.map((rule) => this.createAsyncValidator(rule));
  }

  public initControl(
    control: AbstractControl,
    staticValidators: ValidatorFn[] = [],
    asyncValidators: AsyncValidatorFn[] = []
  ): void {
    this.state.set(control, {
      static: staticValidators,
      dynamic: new Map(),
      async: new Map(),
    });

    asyncValidators.forEach((validator, index) => {
      this.state.get(control)?.async.set(`init_${index}`, validator);
    });

    this.apply(control);
  }

  public addValidator(
    control: AbstractControl,
    name: string,
    params?: Record<string, any>
  ): void {
    const state = this.state.get(control);
    if (!state) return;

    const factory = this.validatorRegistry.get(name);
    if (!factory) return;

    state.dynamic.set(name, factory(params));
    this.apply(control);
  }

  public addAsyncValidator(
    control: AbstractControl,
    name: string,
    rule: IAsyncValidationRule
  ): void {
    const state = this.state.get(control);
    if (!state) return;

    state.async.set(name, this.createAsyncValidator(rule));
    this.apply(control);
  }

  public removeValidator(control: AbstractControl, name: string): void {
    const state = this.state.get(control);
    if (!state) return;

    state.dynamic.delete(name);
    state.async.delete(name);
    this.apply(control);
  }

  public validateCrossField(
    form: AbstractControl,
    rules: any[]
  ): { [key: string]: any } | null {
    const errors: { [key: string]: any } = {};

    rules.forEach((rule) => {
      const result = rule.validator(form.value);
      if (result !== true && result !== null) {
        errors[rule.fields.join('_')] = {
          crossField: rule.message || 'Cross-field validation failed',
        };
      }
    });

    return Object.keys(errors).length > 0 ? errors : null;
  }

  private createAsyncValidator(rule: IAsyncValidationRule): AsyncValidatorFn {
    return (control: AbstractControl): Observable<any> => {
      if (!control.value) return of(null);

      return of(control.value).pipe(
        debounceTime(rule.debounce || 300),
        switchMap((value) => {
          if (rule.validator) {
            return this.handleAsyncValidatorResult(
              rule.validator(value, null as any, null as any)
            );
          }

          if (rule.endpoint) {
            return this.handleApiValidation(rule, value);
          }

          return of(null);
        }),
        catchError(() =>
          of({ asyncError: rule.message || 'Validation failed' })
        )
      );
    };
  }

  private handleAsyncValidatorResult(
    result: Promise<boolean | string>
  ): Observable<any> {
    return new Observable((observer) => {
      result
        .then((res) => {
          if (res === true) {
            observer.next(null);
          } else {
            observer.next({
              async: typeof res === 'string' ? res : 'Validation failed',
            });
          }
          observer.complete();
        })
        .catch(() => {
          observer.next({ async: 'Validation error' });
          observer.complete();
        });
    });
  }

  private handleApiValidation(
    rule: IAsyncValidationRule,
    value: any
  ): Observable<any> {
    return new Observable((observer) => {
      fetch(rule.endpoint!, {
        method: rule.method || 'GET',
        headers: { 'Content-Type': 'application/json', ...rule.headers },
        body:
          rule.method === 'POST'
            ? JSON.stringify({ value, ...rule.params })
            : undefined,
      })
        .then((response) => response.json())
        .then((data) => {
          const isValid = rule.successField
            ? data[rule.successField]
            : data.valid;
          const errorMsg = rule.errorField
            ? data[rule.errorField]
            : data.message;

          observer.next(isValid ? null : { api: errorMsg || rule.message });
          observer.complete();
        })
        .catch(() => {
          observer.next({ api: rule.message || 'Validation failed' });
          observer.complete();
        });
    });
  }

  private apply(control: AbstractControl): void {
    const state = this.state.get(control);
    if (!state) return;

    const allValidators = [...state.static, ...state.dynamic.values()];
    const allAsyncValidators = [...state.async.values()];

    control.setValidators(allValidators.length > 0 ? allValidators : null);
    control.setAsyncValidators(
      allAsyncValidators.length > 0 ? allAsyncValidators : null
    );
    control.updateValueAndValidity({ emitEvent: false });
  }
}
