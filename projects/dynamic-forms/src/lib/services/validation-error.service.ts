import { Injectable } from '@angular/core';
import { IFormI18n } from '../interfaces';

interface ErrorMessageConfig {
  [key: string]: string | ((params?: any) => string);
}

@Injectable()
export class ValidationErrorService {
  private readonly defaultMessages: ErrorMessageConfig = {
    required: 'This field is required',
    email: 'Please enter a valid email address',
    minLength: (params) =>
      `Minimum length is ${params?.requiredLength} characters`,
    maxLength: (params) =>
      `Maximum length is ${params?.requiredLength} characters`,
    min: (params) => `Minimum value is ${params?.min}`,
    max: (params) => `Maximum value is ${params?.max}`,
    pattern: 'Please enter a valid format',
    unique: 'This value already exists',
    api: (params) => params?.message || 'Validation failed',
  };

  private customMessages: ErrorMessageConfig = {};
  private i18nConfig?: IFormI18n;

  public setI18nConfig(config: IFormI18n): void {
    this.i18nConfig = config;
  }

  public setCustomMessages(messages: ErrorMessageConfig): void {
    this.customMessages = { ...this.customMessages, ...messages };
  }

  public getErrorMessage(
    errorKey: string,
    errorValue?: any,
    fieldLabel?: string
  ): string {
    let message = this.getMessageTemplate(errorKey);

    if (typeof message === 'function') {
      message = message(errorValue);
    }

    if (this.i18nConfig?.translate) {
      message =
        this.i18nConfig.translate(`validation.${errorKey}`, {
          field: fieldLabel,
          ...errorValue,
        }) || message;
    }

    return message;
  }

  public getFieldErrors(errors: any, fieldLabel?: string): string[] {
    if (!errors) return [];

    return Object.keys(errors).map((key) =>
      this.getErrorMessage(key, errors[key], fieldLabel)
    );
  }

  public formatErrorsForDisplay(
    errors: { [key: string]: any },
    fields: { [key: string]: { label?: string } }
  ): { [key: string]: string[] } {
    const formatted: { [key: string]: string[] } = {};

    Object.keys(errors).forEach((fieldPath) => {
      const fieldError = errors[fieldPath];
      const fieldLabel = fields[fieldPath]?.label || fieldPath;

      if (typeof fieldError === 'string') {
        formatted[fieldPath] = [fieldError];
      } else if (Array.isArray(fieldError)) {
        formatted[fieldPath] = fieldError;
      } else if (typeof fieldError === 'object') {
        formatted[fieldPath] = this.getFieldErrors(fieldError, fieldLabel);
      }
    });

    return formatted;
  }

  private getMessageTemplate(
    errorKey: string
  ): string | ((params?: any) => string) {
    return (
      this.customMessages[errorKey] ||
      this.defaultMessages[errorKey] ||
      'Validation failed'
    );
  }
}
