import { Injectable, Type } from '@angular/core';

@Injectable()
export class LayoutRegistryService {
  private readonly registry = new Map<string, Type<any>>();

  public register(key: string, component: Type<any>): void {
    this.registry.set(key, component);
  }

  public get(key: string): Type<any> | undefined {
    return this.registry.get(key);
  }

  public has(key: string): boolean {
    return this.registry.has(key);
  }

  public remove(key: string): void {
    this.registry.delete(key);
  }
}
