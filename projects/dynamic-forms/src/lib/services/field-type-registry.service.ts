import { Injectable, Type } from '@angular/core';

@Injectable()
export class FieldTypeRegistryService {
  private readonly registry = new Map<string, Type<any>>();

  public register(type: string, component: Type<any>): void {
    this.registry.set(type, component);
  }

  public get(type: string): Type<any> | undefined {
    return this.registry.get(type);
  }

  public has(type: string): boolean {
    return this.registry.has(type);
  }

  public remove(type: string): void {
    this.registry.delete(type);
  }
}
