import { Injectable } from '@angular/core';
import { AbstractControl, ValidatorFn, Validators } from '@angular/forms';
import { colord } from 'colord';

type ValidatorFactory = (params?: Record<string, any>) => ValidatorFn;

@Injectable()
export class ValidatorRegistryService {
  private readonly validators = new Map<string, ValidatorFactory>();

  public register(name: string, factory: ValidatorFactory): void {
    this.validators.set(name, factory);
  }

  public get(name: string): ValidatorFactory | undefined {
    return this.validators.get(name);
  }

  public create(
    name: string,
    params?: Record<string, any>
  ): ValidatorFn | null {
    const factory = this.get(name);
    return factory ? factory(params) : null;
  }

  public registerDefaultValidators(): void {
    this.register('required', () => Validators.required);
    this.register('email', () => Validators.email);
    this.register('min', (params) => Validators.min(params?.value ?? 0));
    this.register('max', (params) => Validators.max(params?.value ?? 100));
    this.register('minLength', (params) =>
      Validators.minLength(params?.value ?? 0)
    );
    this.register('maxLength', (params) =>
      Validators.maxLength(params?.value ?? 255)
    );
    this.register('pattern', (params) =>
      Validators.pattern(params?.pattern ?? '')
    );

    // Add URL validator
    this.register('url', () => (control: AbstractControl) => {
      const value = control.value;

      if (!value) return null;

      const urlPattern = /^https?:\/\/.+/;
      return urlPattern.test(value) ? null : { url: true };
    });

    // Add minItems/maxItems for arrays
    this.register('minItems', (params) => (control: AbstractControl) => {
      const value = control.value;
      if (!Array.isArray(value)) return null;
      return value.length >= (params?.value ?? 0)
        ? null
        : { minItems: { actual: value.length, required: params?.value } };
    });

    this.register('maxItems', (params) => (control: AbstractControl) => {
      const value = control.value;
      if (!Array.isArray(value)) return null;
      return value.length <= (params?.value ?? 100)
        ? null
        : { maxItems: { actual: value.length, max: params?.value } };
    });

    this.register('color', () => (control: AbstractControl) => {
      const value = control.value;
      if (!value) return null; // consider empty as valid or handle required separately

      const isValid = colord(value).isValid();
      return isValid ? null : { color: true };
    });
  }
}
