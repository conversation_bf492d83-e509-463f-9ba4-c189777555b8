import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { IFormState } from '../interfaces';

@Injectable()
export class FormStateService {
  private readonly state$ = new BehaviorSubject<IFormState>(
    this.getInitialState()
  );

  public getState(): Observable<IFormState> {
    return this.state$.asObservable();
  }

  public getCurrentState(): IFormState {
    return this.state$.value;
  }

  public updateState(updates: Partial<IFormState>): void {
    this.state$.next({ ...this.state$.value, ...updates });
  }

  public setValue(path: string, value: any): void {
    const state = this.state$.value;
    this.updateState({
      values: { ...state.values, [path]: value },
      dirty: { ...state.dirty, [path]: true },
    });
  }

  public setError(path: string, error: string | string[]): void {
    const state = this.state$.value;
    this.updateState({
      errors: { ...state.errors, [path]: error },
      isValid: this.checkFormValid({ ...state.errors, [path]: error }),
    });
  }

  public clearError(path: string): void {
    const state = this.state$.value;
    const { [path]: removed, ...errors } = state.errors;
    this.updateState({
      errors,
      isValid: this.checkFormValid(errors),
    });
  }

  public markTouched(path: string): void {
    const state = this.state$.value;
    this.updateState({
      touched: { ...state.touched, [path]: true },
    });
  }

  public setFieldProperty(
    path: string,
    property: 'disabled' | 'hidden',
    value: boolean
  ): void {
    const state = this.state$.value;
    this.updateState({
      [property]: { ...state[property], [path]: value },
    });
  }

  public reset(values?: Record<string, any>): void {
    this.state$.next({
      ...this.getInitialState(),
      values: values || {},
    });
  }

  private getInitialState(): IFormState {
    return {
      values: {},
      errors: {},
      touched: {},
      dirty: {},
      disabled: {},
      hidden: {},
      isValid: true,
      isSubmitting: false,
      isValidating: false,
      submitCount: 0,
    };
  }

  private checkFormValid(errors: Record<string, string | string[]>): boolean {
    return Object.keys(errors).length === 0;
  }
}
