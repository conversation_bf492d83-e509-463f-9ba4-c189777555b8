import { Injectable, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Subscription } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { IConditionalRule, ICondition, IAction } from '../interfaces';
import { FormContextService } from './form-context.service';

@Injectable()
export class ConditionalEngineService implements OnDestroy {
  private readonly subscriptions = new Map<string, Subscription>();

  public constructor(private readonly formContext: FormContextService) {}

  public ngOnDestroy(): void {
    this.cleanup();
  }

  public applyRules(form: FormGroup, rules: IConditionalRule[]): void {
    this.cleanup();

    rules
      .filter((rule) => rule.enabled !== false)
      .sort((a, b) => (b.priority || 0) - (a.priority || 0))
      .forEach((rule) => this.setupRule(form, rule));
  }

  public cleanup(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
    this.subscriptions.clear();
  }

  private setupRule(form: FormGroup, rule: IConditionalRule): void {
    const watchFields = this.extractWatchFields(rule.condition);

    watchFields.forEach((fieldPath: string) => {
      const control = form.get(fieldPath);
      if (!control) return;

      const key = `${rule.id}_${fieldPath}`;
      const subscription = control.valueChanges
        .pipe(debounceTime(rule.debounce || 0))
        .subscribe(() => {
          if (this.evaluateCondition(form, rule.condition)) {
            this.executeActions(form, rule.actions);
          }
        });

      this.subscriptions.set(key, subscription);
    });

    if (this.evaluateCondition(form, rule.condition)) {
      this.executeActions(form, rule.actions);
    }
  }

  private extractWatchFields(condition: ICondition): string[] {
    const fields: string[] = [];

    if (condition.field) {
      fields.push(condition.field);
    }

    condition.and?.forEach((c) => fields.push(...this.extractWatchFields(c)));
    condition.or?.forEach((c) => fields.push(...this.extractWatchFields(c)));

    if (condition.not) {
      fields.push(...this.extractWatchFields(condition.not));
    }

    return [...new Set(fields)];
  }

  private evaluateCondition(form: FormGroup, condition: ICondition): boolean {
    if (condition.custom) {
      return condition.custom(form.value);
    }

    if (condition.expression) {
      return this.evaluateExpression(condition.expression, form.value);
    }

    if (condition.field && condition.operator) {
      return this.evaluateFieldCondition(form, condition);
    }

    if (condition.and) {
      return condition.and.every((c) => this.evaluateCondition(form, c));
    }

    if (condition.or) {
      return condition.or.some((c) => this.evaluateCondition(form, c));
    }

    if (condition.not) {
      return !this.evaluateCondition(form, condition.not);
    }

    return false;
  }

  private evaluateFieldCondition(
    form: FormGroup,
    condition: ICondition
  ): boolean {
    const control = form.get(condition.field!);
    if (!control) return false;

    const value = control.value;
    const conditionValue = condition.value;

    switch (condition.operator) {
      case 'equals':
        return value === conditionValue;
      case 'notEquals':
        return value !== conditionValue;
      case 'contains':
        return value?.toString().includes(conditionValue);
      case 'notContains':
        return !value?.toString().includes(conditionValue);
      case 'startsWith':
        return value?.toString().startsWith(conditionValue);
      case 'endsWith':
        return value?.toString().endsWith(conditionValue);
      case 'isEmpty':
        return !value || value.length === 0;
      case 'notEmpty':
        return value && value.length > 0;
      case 'greaterThan':
        return Number(value) > Number(conditionValue);
      case 'lessThan':
        return Number(value) < Number(conditionValue);
      case 'greaterOrEqual':
        return Number(value) >= Number(conditionValue);
      case 'lessOrEqual':
        return Number(value) <= Number(conditionValue);
      case 'between': {
        const [min, max] = Array.isArray(conditionValue)
          ? conditionValue
          : [0, 0];
        return Number(value) >= min && Number(value) <= max;
      }
      case 'notBetween': {
        const [minNot, maxNot] = Array.isArray(conditionValue)
          ? conditionValue
          : [0, 0];
        return Number(value) < minNot || Number(value) > maxNot;
      }
      case 'in':
        return Array.isArray(conditionValue) && conditionValue.includes(value);
      case 'notIn':
        return (
          !Array.isArray(conditionValue) || !conditionValue.includes(value)
        );
      case 'regex': {
        const regex = new RegExp(conditionValue);
        return regex.test(value?.toString() || '');
      }
      case 'truthy':
        return !!value;
      case 'falsy':
        return !value;
      default:
        return false;
    }
  }

  private evaluateExpression(expression: string, formValue: any): boolean {
    try {
      const func = new Function('form', `return ${expression}`);
      return !!func(formValue);
    } catch {
      return false;
    }
  }

  private executeActions(form: FormGroup, actions: IAction[]): void {
    actions.forEach((action) => {
      const targets = Array.isArray(action.target)
        ? action.target
        : [action.target];

      targets.forEach((target) => {
        const control = form.get(target);
        if (!control) return;

        switch (action.type) {
          case 'show':
            this.formContext.setFieldProperty(target, 'hidden', false);
            break;
          case 'hide':
            this.formContext.setFieldProperty(target, 'hidden', true);
            break;
          case 'enable':
            control.enable();
            this.formContext.setFieldProperty(target, 'disabled', false);
            break;
          case 'disable':
            control.disable();
            this.formContext.setFieldProperty(target, 'disabled', true);
            break;
          case 'setValue':
            this.formContext.setValue(target, action.value);
            break;
          case 'clearValue':
            this.formContext.setValue(target, null);
            break;
          case 'setError':
            this.formContext.setError(target, action.error || 'Error');
            break;
          case 'clearError':
            this.formContext.clearError(target);
            break;
          case 'validate':
            this.formContext.validateField(target);
            break;
          case 'focus':
            this.focusElement(target);
            break;
          case 'custom':
            if (action.custom) {
              action.custom(this.formContext.fields[target], form.value);
            }
            break;
        }
      });
    });
  }

  private focusElement(fieldName: string): void {
    setTimeout(() => {
      const element = document.querySelector(
        `[name="${fieldName}"]`
      ) as HTMLElement;
      element?.focus();
    }, 0);
  }
}
