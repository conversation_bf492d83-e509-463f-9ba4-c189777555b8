import { Injectable } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormArray,
  FormControl,
  AbstractControl,
} from '@angular/forms';
import { IFormConfig, IField } from '../interfaces';
import { extractFields, extractFieldsFromRows } from '../utils';
import { ValidatorManagerService } from './validator-manager.service';
import { DISPLAY_ONLY_TYPES } from '../types';
import { getInitialValue } from '../utils/get-initial-value.util';

@Injectable()
export class FormBuilderService {
  constructor(
    private readonly fb: FormBuilder,
    private readonly validatorManagerService: ValidatorManagerService
  ) {}

  public buildForm(config: IFormConfig): FormGroup {
    const form = this.fb.group({});
    const fields = extractFields(config.layout);

    fields.forEach((field) => {
      if (DISPLAY_ONLY_TYPES.has(field.type)) return;

      const control = this.createControl(field);
      if (form.contains(field.name)) {
        throw new Error(`Duplicate control name: ${field.name}`);
      }

      form.addControl(field.name, control);
    });

    return form;
  }

  // --------------------------
  // Control creation
  // --------------------------
  private createControl(field: IField): AbstractControl {
    switch (field.type) {
      case 'group':
        return this.createGroup(field);
      case 'array':
        return this.createArray(field);
      default:
        return this.createFormControl(field);
    }
  }

  private createFormControl(field: IField): FormControl {
    const control = this.fb.control({
      value: getInitialValue(field),
      disabled: field.disabled || field.readonly,
    });
    this.applyValidators(control, field);
    return control;
  }

  private createGroup(field: IField): FormGroup {
    const group = this.fb.group({});
    this.applyValidators(group, field);

    const nestedFields = extractFieldsFromRows(field.groupConfig?.rows ?? []);
    nestedFields.forEach((nestedField) => {
      group.addControl(nestedField.name, this.createControl(nestedField));
    });

    return group;
  }

  private createArray(field: IField): FormArray {
    const array = this.fb.array([]);
    this.applyValidators(array, field);

    const initialItems =
      field.arrayConfig?.initialItems ?? field.arrayConfig?.minItems ?? 0;

    for (let i = 0; i < initialItems; i++) {
      array.push(this.createArrayItem(field));
    }

    return array;
  }

  private createArrayItem(field: IField): AbstractControl {
    const { arrayConfig } = field;
    if (!arrayConfig) {
      throw new Error(`Missing arrayConfig for field: ${field.name}`);
    }

    if (arrayConfig.template.field) {
      return this.createControl(arrayConfig.template.field);
    }

    if (arrayConfig.template.rows) {
      const group = this.fb.group({});
      const itemFields = extractFieldsFromRows(arrayConfig.template.rows);
      itemFields.forEach((itemField) => {
        group.addControl(itemField.name, this.createControl(itemField));
      });
      return group;
    }

    throw new Error(`Invalid array template for field: ${field.name}`);
  }

  // --------------------------
  // Utilities
  // --------------------------
  private applyValidators(control: AbstractControl, field: IField): void {
    const validators = this.validatorManagerService.build(field);
    this.validatorManagerService.initControl(control, validators);
  }
}
