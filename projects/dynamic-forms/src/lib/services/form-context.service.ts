import { Injectable } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IFormContext, IField } from '../interfaces';
import { FormStateService } from './form-state.service';
import { ArrayFieldService } from './array-field.service';

type FormStateType = ReturnType<FormStateService['getCurrentState']>;

@Injectable()
export class FormContextService implements IFormContext {
  public form = this.formState.getCurrentState();
  public fields: Record<string, IField> = {};

  private formGroup!: FormGroup;

  public constructor(
    private readonly formState: FormStateService,
    private readonly arrayService: ArrayFieldService
  ) {
    this.formState.getState().subscribe((state) => (this.form = state));
  }

  public setFormGroup(formGroup: FormGroup): void {
    this.formGroup = formGroup;
  }

  public getValue(path: string): any {
    return this.formGroup.get(path)?.value;
  }

  public setValue(path: string, value: any): void {
    const control = this.formGroup.get(path);
    if (!control) return;
    control.setValue(value);
    this.formState.setValue(path, value);
    this.validateField(path);
  }

  public getError(path: string): string | string[] | undefined {
    return this.form.errors[path];
  }

  public setError(path: string, error: string | string[]): void {
    this.formState.setError(path, error);
  }

  public clearError(path: string): void {
    this.formState.clearError(path);
  }

  public async validate(path?: string): Promise<boolean> {
    if (path) {
      return this.validateField(path);
    }

    this.formState.updateState({ isValidating: true });

    for (const fieldPath in this.fields) {
      await this.validateField(fieldPath);
    }

    this.formState.updateState({ isValidating: false });
    return this.form.isValid;
  }

  public async validateField(path: string): Promise<boolean> {
    const control = this.formGroup.get(path);
    if (!control) return true;

    control.markAsTouched();
    this.formState.markTouched(path);

    if (control.valid) {
      this.clearError(path);
      return true;
    }

    const errors = Object.keys(control.errors || {});
    this.setError(path, errors[0]);
    return false;
  }

  public async submit(): Promise<void> {
    this.formState.updateState({
      isSubmitting: true,
      submitCount: this.form.submitCount + 1,
    });

    const isValid = await this.validate();

    if (isValid) {
      // Submit logic here
    }

    this.formState.updateState({ isSubmitting: false });
  }

  public reset(values?: any): void {
    this.formGroup.reset(values);
    this.formGroup.markAsPristine();
    this.formGroup.markAsUntouched();
    this.formState.reset(values);
  }

  public setFieldProperty(path: string, property: string, value: any): void {
    if (property === 'disabled' || property === 'hidden') {
      this.formState.setFieldProperty(path, property, value);
    }
  }

  public getFieldProperty(path: string, property: string): any {
    switch (property) {
      case 'disabled':
        return this.form.disabled[path];
      case 'hidden':
        return this.form.hidden[path];
      case 'touched':
        return this.form.touched[path];
      case 'dirty':
        return this.form.dirty[path];
      case 'error':
        return this.form.errors[path];
      case 'value':
        return this.form.values[path];
      default:
        return undefined;
    }
  }

  public addArrayItem(path: string, value?: any, index?: number): void {
    this.arrayService.addItem(this.formGroup, path, value, index);
  }

  public removeArrayItem(path: string, index: number): void {
    this.arrayService.removeItem(this.formGroup, path, index);
  }

  public moveArrayItem(path: string, from: number, to: number): void {
    this.arrayService.moveItem(this.formGroup, path, from, to);
  }

  public getState(): FormStateType {
    return this.form;
  }

  public setState(state: Partial<FormStateType>): void {
    this.formState.updateState(state);
  }

  public subscribe(callback: (state: FormStateType) => void): () => void {
    const sub = this.formState.getState().subscribe(callback);
    return () => sub.unsubscribe();
  }
}
