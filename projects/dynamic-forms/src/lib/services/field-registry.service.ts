import { Injectable } from '@angular/core';
import { AbstractControl } from '@angular/forms';

@Injectable()
export class FieldRegistryService {
  private readonly fieldMap = new Map<string, AbstractControl>();

  /**
   * Register a control for a given fieldId
   */
  public registerField(fieldId: string, control: AbstractControl): void {
    if (!fieldId || !control) return;
    this.fieldMap.set(fieldId, control);
  }

  /**
   * Get a control by fieldId
   */
  public getControl(fieldId: string): AbstractControl | undefined {
    return this.fieldMap.get(fieldId);
  }

  /**
   * Remove a control from registry
   */
  public unregisterField(fieldId: string): void {
    this.fieldMap.delete(fieldId);
  }

  /**
   * Check if field exists
   */
  public hasField(fieldId: string): boolean {
    return this.fieldMap.has(fieldId);
  }

  /**
   * Clear all registered fields (e.g., on form destroy)
   */
  public clear(): void {
    this.fieldMap.clear();
  }
}
