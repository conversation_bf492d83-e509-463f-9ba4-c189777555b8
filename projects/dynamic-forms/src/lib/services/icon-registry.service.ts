import { Injectable } from '@angular/core';
import { IIconOption } from '../interfaces';

@Injectable()
export class IconRegistryService {
  private readonly iconSets = new Map<string, IIconOption[]>();

  // Register a set of icons under a name
  public registerIconSet(name: string, icons: IIconOption[]): void {
    this.iconSets.set(name, icons);
  }

  // Retrieve icons by set name
  public getIconSet(name: string): IIconOption[] {
    return this.iconSets.get(name) ?? [];
  }

  // Search icons by query string optionally within a specific icon set
  public searchIcons(query: string, setName?: string): IIconOption[] {
    const sets: IIconOption[][] = setName
      ? [this.iconSets.get(setName) ?? []]
      : Array.from(this.iconSets.values());

    const allIcons = sets.flat();
    const searchTerm = query.toLowerCase();

    return allIcons.filter((icon) => {
      const labelMatch = icon.label?.toLowerCase().includes(searchTerm);
      const valueMatch = icon.value
        ?.toString()
        .toLowerCase()
        .includes(searchTerm);
      const tagMatch = icon.tags?.some((tag) =>
        tag.toLowerCase().includes(searchTerm)
      );
      const keywordMatch = icon.keywords?.some((kw) =>
        kw.toLowerCase().includes(searchTerm)
      );
      return labelMatch || valueMatch || tagMatch || keywordMatch;
    });
  }
}
