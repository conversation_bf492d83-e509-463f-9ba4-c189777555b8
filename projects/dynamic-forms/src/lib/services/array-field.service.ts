import { Injectable } from '@angular/core';
import { FormArray, FormGroup, AbstractControl } from '@angular/forms';

@Injectable()
export class ArrayFieldService {
  public addItem(
    form: FormGroup,
    path: string,
    value?: any,
    index?: number
  ): void {
    const array = form.get(path) as FormArray;
    if (!array) return;

    const control = this.createArrayItemControl(array, value);

    if (index !== undefined) {
      array.insert(index, control);
    } else {
      array.push(control);
    }
  }

  public removeItem(form: FormGroup, path: string, index: number): void {
    const array = form.get(path) as FormArray;
    if (!array || index < 0 || index >= array.length) return;

    array.removeAt(index);
  }

  public moveItem(
    form: FormGroup,
    path: string,
    from: number,
    to: number
  ): void {
    const array = form.get(path) as FormArray;
    if (
      !array ||
      from < 0 ||
      to < 0 ||
      from >= array.length ||
      to >= array.length
    )
      return;

    const control = array.at(from);
    array.removeAt(from);
    array.insert(to, control);
  }

  private createArrayItemControl(
    array: FormArray,
    value?: any
  ): AbstractControl {
    const firstControl = array.at(0);
    if (!firstControl) {
      throw new Error('Cannot create array item: no template control found');
    }

    if (firstControl instanceof FormGroup) {
      const newGroup = new FormGroup({});
      Object.keys(firstControl.controls).forEach((key) => {
        const control = firstControl.get(key);
        if (control) {
          newGroup.addControl(key, this.cloneControl(control));
        }
      });
      if (value) newGroup.patchValue(value);
      return newGroup;
    }

    const newControl = this.cloneControl(firstControl);
    if (value !== undefined) newControl.setValue(value);
    return newControl;
  }

  private cloneControl(control: AbstractControl): AbstractControl {
    const cloned = control.constructor(
      control.value,
      control.validator,
      control.asyncValidator
    );

    if (control.disabled) cloned.disable();
    return cloned;
  }
}
