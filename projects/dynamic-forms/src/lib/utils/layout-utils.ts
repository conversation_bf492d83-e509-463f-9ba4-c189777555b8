import {
  IFormLayout,
  ISection,
  IWizardStep,
  ITab,
  IAccordionPanel,
  IRow,
  IColumn,
  IField,
} from '../interfaces';

export function extractFields(layout?: IFormLayout): IField[] {
  if (!layout) return [];

  switch (layout.type) {
    case 'sections':
      return extractFieldsFromSections(layout.sections ?? []);
    case 'wizard':
      return extractFieldsFromWizard(layout.steps ?? []);
    case 'tabs':
      return extractFieldsFromTabs(layout.tabs ?? []);
    case 'accordion':
      return extractFieldsFromAccordion(layout.panels ?? []);
    default:
      return [];
  }
}

export function extractFieldsFromSections(sections: ISection[]): IField[] {
  return sections.flatMap((section) => extractFieldsFromRows(section.rows));
}

export function extractFieldsFromWizard(steps: IWizardStep[]): IField[] {
  return steps.flatMap((step) =>
    extractFieldsFromSections(step.sections ?? [])
  );
}

export function extractFieldsFromTabs(tabs: ITab[]): IField[] {
  return tabs.flatMap((tab) => extractFieldsFromSections(tab.sections ?? []));
}

export function extractFieldsFromAccordion(
  panels: IAccordionPanel[]
): IField[] {
  return panels.flatMap((panel) =>
    extractFieldsFromSections(panel.sections ?? [])
  );
}

export function extractFieldsFromRows(rows: IRow[]): IField[] {
  return rows.flatMap((row) => extractFieldsFromRow(row));
}

export function extractFieldsFromRow(row: IRow): IField[] {
  return row.columns.flatMap((col) => extractFieldsFromColumn(col));
}

function extractFieldsFromColumn(column: IColumn): IField[] {
  return column.fields.flatMap((field) => {
    const fields: IField[] = [field];

    if (field.type === 'group' && field.groupConfig?.rows) {
      fields.push(...extractFieldsFromRows(field.groupConfig.rows));
    }

    return fields;
  });
}
