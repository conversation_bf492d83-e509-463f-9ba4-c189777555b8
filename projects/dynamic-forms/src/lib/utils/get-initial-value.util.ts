import { IField } from '../interfaces';

export const getInitialValue = (field: IField): unknown => {
  if (field.value !== undefined) return field.value;
  if (field.defaultValue !== undefined) return field.defaultValue;

  const defaults: Record<string, unknown> = {
    checkbox: false,
    toggle: false,
    switch: false,
    number: field.min ?? 0,
    range: field.min ?? 0,
    slider: field.min ?? 0,
    multiselect: [],
    tags: [],
    chips: [],
    date: null,
    datetime: null,
    time: null,
  };

  return defaults[field.type] ?? '';
};
