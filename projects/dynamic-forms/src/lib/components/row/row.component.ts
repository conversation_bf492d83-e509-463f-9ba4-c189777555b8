import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IRow } from '../../interfaces';

@Component({
  selector: 'lib-row',
  template: `
    <div
      class="grid-row"
      [class]="row.className"
      [ngStyle]="rowStyles"
      [hidden]="row.hidden"
    >
      <lib-column
        *ngFor="let column of row.columns"
        [column]="column"
        [form]="form"
      ></lib-column>
    </div>
  `,
  styles: [
    `
      .grid-row {
        display: grid;
        grid-template-columns: repeat(12, 1fr);
        gap: 1rem; /* Default gap */
        width: 100%;
      }
    `,
  ],
})
export class RowComponent implements OnChanges {
  @Input() public row!: IRow;
  @Input() public form!: FormGroup;

  public rowStyles: any = {};

  public ngOnChanges(changes: SimpleChanges): void {
    if (changes['row']) this.computeRowStyle();
  }

  private computeRowStyle(): void {
    this.rowStyles = {
      ...this.row.style,
      ...(this.row.gap && { gap: this.row.gap }),
      ...(this.row.align && { alignItems: this.getAlignValue(this.row.align) }),
      ...(this.row.justify && {
        justifyContent: this.getJustifyValue(this.row.justify),
      }),
    };
  }

  private getAlignValue(align: string): string {
    const alignMap: { [key: string]: string } = {
      start: 'start',
      center: 'center',
      end: 'end',
      stretch: 'stretch',
    };
    return alignMap[align] || 'stretch';
  }

  private getJustifyValue(justify: string): string {
    const justifyMap: { [key: string]: string } = {
      start: 'start',
      center: 'center',
      end: 'end',
      'space-between': 'space-between',
      'space-around': 'space-around',
    };
    return justifyMap[justify] || 'start';
  }
}
