import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IField } from '../../interfaces';
import { FormContextService } from '../../services';

@Component({
  selector: 'lib-field-wrapper',
  template: `
    <div
      [ngClass]="field.className ?? ''"
      [ngStyle]="field.style ?? {}"
      [hidden]="isHidden"
    >
      <label
        *ngIf="field.label && labelPosition === 'top'"
        [for]="field.id"
        class="block text-sm font-medium text-gray-700 mb-1"
        [class.required]="isRequired"
      >
        {{ field.label }}
        <span
          *ngIf="field.tooltip"
          class="inline-block w-4 h-4 bg-blue-500 text-white rounded-full text-center text-xs leading-4 ml-1 cursor-help"
          [title]="field.tooltip"
          >?</span
        >
      </label>

      <div
        class="flex"
        [ngClass]="{
          'flex-row': labelPosition === 'left' || labelPosition === 'right',
          'flex-col': labelPosition === 'top'
        }"
      >
        <label
          *ngIf="field.label && labelPosition === 'left'"
          [for]="field.id"
          class="min-w-[120px] mr-3 text-sm font-medium text-gray-700"
          [class.required]="isRequired"
        >
          {{ field.label }}
        </label>

        <div class="relative flex items-center flex-1">
          <span
            *ngIf="field.prefix"
            class="px-3 py-2 bg-gray-50 border border-r-0 border-gray-300 text-gray-500 rounded-l-md"
          >
            {{ field.prefix }}
          </span>

          <div>
            <ng-content></ng-content>
          </div>

          <span
            *ngIf="field.suffix"
            class="px-3 py-2 bg-gray-50 border border-l-0 border-gray-300 text-gray-500 rounded-r-md"
          >
            {{ field.suffix }}
          </span>
          <span *ngIf="field.icon" class="absolute right-3 text-gray-400">
            {{ field.icon }}
          </span>
        </div>

        <label
          *ngIf="field.label && labelPosition === 'right'"
          [for]="field.id"
          class="ml-3 text-sm font-medium text-gray-700"
          [class.required]="isRequired"
        >
          {{ field.label }}
        </label>
      </div>

      <div *ngIf="field.helpText" class="mt-1 text-sm text-gray-500">
        {{ field.helpText }}
      </div>

      <div *ngIf="hasError" class="mt-1">
        <span
          *ngFor="let error of errorMessages"
          class="block text-sm text-red-600"
        >
          {{ error }}
        </span>
      </div>
    </div>
  `,
  styles: [
    `
      label.required::after {
        content: '*';
        color: #dc2626; /* red-600 */
        margin-left: 0.25rem; /* ml-1 */
      }
    `,
  ],
})
export class FieldWrapperComponent implements OnInit {
  @Input() public field!: IField;
  @Input() public form!: FormGroup;

  public labelPosition: string = 'top';
  public isRequired: boolean = false;
  public isHidden: boolean = false;
  public hasError: boolean = false;
  public errorMessages: string[] = [];

  public constructor(private readonly formContext: FormContextService) {}

  public ngOnInit(): void {
    this.labelPosition = this.field.labelPosition || 'top';
    this.updateState();

    this.formContext.subscribe(() => this.updateState());
  }

  private updateState(): void {
    this.isRequired = !!this.field.required;
    this.isHidden =
      this.formContext.getFieldProperty(this.field.name, 'hidden') || false;

    const errors = this.formContext.getError(this.field.name);
    this.hasError = !!errors;
    if (Array.isArray(errors)) this.errorMessages = errors;
    else if (errors) this.errorMessages = [errors];
    else this.errorMessages = [];
  }
}
