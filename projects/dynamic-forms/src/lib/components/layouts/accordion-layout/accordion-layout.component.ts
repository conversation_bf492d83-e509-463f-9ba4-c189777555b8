import { Component, Input } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IFormLayout, IAccordionPanel } from '../../../interfaces';

@Component({
  selector: 'lib-accordion-layout',
  template: `
    <div
      class="flex flex-col gap-2"
      [ngClass]="layout.className ?? ''"
      [ngStyle]="layout.style ?? null"
    >
      <div
        *ngFor="let panel of layout.panels; let i = index"
        class="border border-gray-200 rounded-md overflow-hidden"
        [ngClass]="{ 'border-b-gray-200': panel.expanded }"
      >
        <div
          class="p-4 bg-gray-50 cursor-pointer flex items-center gap-3 hover:bg-gray-100 transition-colors"
          [ngClass]="{
            'cursor-not-allowed opacity-50': panel.disabled,
            'border-b border-gray-200': panel.expanded
          }"
          (click)="togglePanel(panel)"
        >
          <span *ngIf="panel.icon">{{ panel.icon }}</span>
          <div class="flex-1">
            <h3 class="text-base font-semibold m-0">{{ panel.title }}</h3>
            <p *ngIf="panel.subtitle" class="text-sm text-gray-500 m-0">
              {{ panel.subtitle }}
            </p>
          </div>
          <span class="text-xl font-bold text-gray-400">
            {{ panel.expanded ? '-' : '+' }}
          </span>
        </div>

        <div class="p-4" [hidden]="!panel.expanded">
          <lib-section
            *ngFor="let section of panel.sections"
            [section]="section"
            [form]="form"
          >
          </lib-section>
        </div>
      </div>
    </div>
  `,
})
export class AccordionLayoutComponent {
  @Input() public layout!: IFormLayout;
  @Input() public form!: FormGroup;

  public togglePanel(panel: IAccordionPanel): void {
    if (!panel.disabled) {
      panel.expanded = !panel.expanded;
    }
  }
}
