import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IFormLayout, IWizardStep, IButtonConfig } from '../../../interfaces';
import { FormContextService } from '../../../services';

export interface ProcessedWizardButton {
  type: string;
  text: string;
  icon?: string;
  showLeftIcon: boolean;
  showRightIcon: boolean;
  style: any;
  disabled: boolean;
  originalConfig: IButtonConfig;
}

@Component({
  selector: 'lib-wizard-layout',
  template: `
    <div
      class="flex flex-col min-h-[400px]"
      [class]="layout.className"
      [ngStyle]="layout.style ?? {}"
    >
      <!-- Progress Header -->
      <div class="border-b border-gray-200 pb-4 mb-8">
        <div class="relative flex justify-between">
          <!-- Progress line -->
          <div
            class="absolute top-5 left-5 right-5 h-0.5 bg-gray-200 z-0"
          ></div>

          <div
            *ngFor="let step of layout.steps; let i = index"
            class="relative z-10 flex flex-col items-center px-2"
          >
            <div
              class="w-10 h-10 rounded-full flex items-center justify-center font-semibold mb-2 transition-colors"
              [ngClass]="[
                i === currentStepIndex ? 'bg-blue-500 text-white' : '',
                i < currentStepIndex ? 'bg-green-500 text-white' : '',
                i > currentStepIndex ? 'bg-gray-200 text-gray-600' : ''
              ]"
            >
              {{ i + 1 }}
            </div>
            <span class="text-sm font-medium text-center">
              {{ step.title }}
            </span>
          </div>
        </div>
      </div>

      <!-- Step Content -->
      <div class="flex-1 py-4">
        <div *ngIf="currentStep">
          <h2 *ngIf="currentStep.title" class="text-2xl font-bold mb-2">
            {{ currentStep.title }}
          </h2>
          <p *ngIf="currentStep.subtitle" class="text-lg text-gray-600 mb-2">
            {{ currentStep.subtitle }}
          </p>
          <p *ngIf="currentStep.description" class="text-gray-600 mb-6">
            {{ currentStep.description }}
          </p>

          <lib-section
            *ngFor="let section of currentStep.sections"
            [section]="section"
            [form]="form"
          >
          </lib-section>
        </div>
      </div>

      <!-- Navigation Footer -->
      <div class="flex justify-between pt-6 border-t border-gray-200 mt-8">
        <!-- Back Button -->
        <button
          *ngIf="backButton"
          [type]="backButton.type"
          [disabled]="backButton.disabled"
          [ngClass]="[
            backButtonVariantPrimary && !backButton.disabled
              ? 'bg-blue-500 hover:bg-blue-600 text-white'
              : '',
            backButtonVariantSecondary && !backButton.disabled
              ? 'bg-gray-500 hover:bg-gray-600 text-white'
              : '',
            backButtonVariantLight && !backButton.disabled
              ? 'bg-gray-100 text-gray-900 hover:bg-gray-200'
              : '',
            backButton.disabled
              ? 'opacity-50 cursor-not-allowed bg-gray-300 text-gray-500'
              : '',
            'px-4 py-2 rounded-md font-medium transition-colors'
          ]"
          (click)="onBackClick()"
        >
          <span *ngIf="backButton.showLeftIcon">{{ backButton.icon }}</span>
          {{ backButton.text }}
          <span *ngIf="backButton.showRightIcon">{{ backButton.icon }}</span>
        </button>

        <!-- Spacer when no back button -->
        <div *ngIf="!backButton"></div>

        <!-- Right side buttons -->
        <div class="flex gap-3">
          <!-- Skip Button -->
          <button
            *ngIf="skipButton"
            [type]="skipButton.type"
            [disabled]="skipButton.disabled"
            [ngClass]="[
              skipButtonVariantSecondary && !skipButton.disabled
                ? 'bg-gray-500 hover:bg-gray-600 text-white'
                : '',
              skipButtonVariantLight && !skipButton.disabled
                ? 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                : '',
              skipButtonVariantLink && !skipButton.disabled
                ? 'bg-transparent text-blue-500 hover:text-blue-600 hover:underline'
                : '',
              skipButton.disabled
                ? 'opacity-50 cursor-not-allowed bg-gray-300 text-gray-500'
                : '',
              'px-4 py-2 rounded-md font-medium transition-colors'
            ]"
            (click)="onSkipClick()"
          >
            <span *ngIf="skipButton.showLeftIcon">{{ skipButton.icon }}</span>
            {{ skipButton.text }}
            <span *ngIf="skipButton.showRightIcon">{{ skipButton.icon }}</span>
          </button>

          <!-- Next/Submit Button -->
          <button
            *ngIf="nextButton"
            [type]="nextButton.type"
            [disabled]="nextButton.disabled"
            [ngClass]="[
              nextButtonVariantPrimary && !nextButton.disabled
                ? 'bg-blue-500 hover:bg-blue-600 text-white'
                : '',
              nextButtonVariantSuccess && !nextButton.disabled
                ? 'bg-green-500 hover:bg-green-600 text-white'
                : '',
              nextButtonVariantSecondary && !nextButton.disabled
                ? 'bg-gray-500 hover:bg-gray-600 text-white'
                : '',
              nextButton.disabled
                ? 'opacity-50 cursor-not-allowed bg-gray-300 text-gray-500'
                : '',
              'px-4 py-2 rounded-md font-medium transition-colors'
            ]"
            (click)="onNextClick()"
          >
            <span *ngIf="nextButton.showLeftIcon">{{ nextButton.icon }}</span>
            {{ nextButton.text }}
            <span *ngIf="nextButton.showRightIcon">{{ nextButton.icon }}</span>
          </button>

          <!-- Custom Buttons -->
          <button
            *ngFor="let button of customButtons"
            [type]="button.type"
            [disabled]="button.disabled"
            [ngClass]="[
              button.originalConfig.variant === 'primary' && !button.disabled
                ? 'bg-blue-500 hover:bg-blue-600 text-white'
                : '',
              button.originalConfig.variant === 'secondary' && !button.disabled
                ? 'bg-gray-500 hover:bg-gray-600 text-white'
                : '',
              button.originalConfig.variant === 'success' && !button.disabled
                ? 'bg-green-500 hover:bg-green-600 text-white'
                : '',
              button.originalConfig.variant === 'danger' && !button.disabled
                ? 'bg-red-500 hover:bg-red-600 text-white'
                : '',
              button.originalConfig.variant === 'info' && !button.disabled
                ? 'bg-cyan-500 hover:bg-cyan-600 text-white'
                : '',
              button.originalConfig.variant === 'light' && !button.disabled
                ? 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                : '',
              button.disabled
                ? 'opacity-50 cursor-not-allowed bg-gray-300 text-gray-500'
                : '',
              'px-4 py-2 rounded-md font-medium transition-colors'
            ]"
            (click)="onCustomButtonClick(button)"
          >
            <span *ngIf="button.showLeftIcon">{{ button.icon }}</span>
            {{ button.text }}
            <span *ngIf="button.showRightIcon">{{ button.icon }}</span>
          </button>
        </div>
      </div>
    </div>
  `,
})
export class WizardLayoutComponent implements OnInit {
  @Input() public layout!: IFormLayout;
  @Input() public form!: FormGroup;

  public currentStepIndex: number = 0;
  public currentStep!: IWizardStep;

  // Navigation buttons
  public backButton: ProcessedWizardButton | null = null;
  public nextButton: ProcessedWizardButton | null = null;
  public skipButton: ProcessedWizardButton | null = null;
  public customButtons: ProcessedWizardButton[] = [];

  // Precomputed variant flags
  public backButtonVariantPrimary = false;
  public backButtonVariantSecondary = false;
  public backButtonVariantLight = false;

  public nextButtonVariantPrimary = false;
  public nextButtonVariantSuccess = false;
  public nextButtonVariantSecondary = false;

  public skipButtonVariantSecondary = false;
  public skipButtonVariantLight = false;
  public skipButtonVariantLink = false;

  public constructor(private readonly formContext: FormContextService) {}

  public ngOnInit(): void {
    this.updateCurrentStep();
    this.updateNavigationButtons();
  }

  public onBackClick(): void {
    if (this.currentStepIndex > 0) {
      this.currentStepIndex--;
      this.updateCurrentStep();
      this.updateNavigationButtons();
    }
  }

  public onNextClick(): void {
    const isLastStep =
      this.currentStepIndex === (this.layout.steps?.length || 0) - 1;

    if (isLastStep) {
      // Submit the form
      this.formContext.submit();
    } else if (this.canGoNext()) {
      this.currentStepIndex++;
      this.updateCurrentStep();
      this.updateNavigationButtons();
    }
  }

  public onSkipClick(): void {
    if (this.currentStep.skippable && this.canSkip()) {
      this.currentStepIndex++;
      this.updateCurrentStep();
      this.updateNavigationButtons();
    }
  }

  public onCustomButtonClick(button: ProcessedWizardButton): void {
    // Emit custom button event or handle custom logic
    console.log('Custom wizard button clicked:', button.originalConfig);
  }

  private updateCurrentStep(): void {
    this.currentStep = this.layout.steps?.[this.currentStepIndex]!;
  }

  private updateNavigationButtons(): void {
    this.backButton = this.processBackButton();
    this.nextButton = this.processNextButton();
    this.skipButton = this.processSkipButton();
    this.customButtons = this.processCustomButtons();

    this.precomputeButtonVariants();
  }

  private processBackButton(): ProcessedWizardButton | null {
    if (this.currentStepIndex === 0) return null;

    const navButtons = this.currentStep.navigationButtons;
    if (!navButtons?.back) {
      // Default back button
      return this.processWizardButton({
        text: 'Back',
        type: 'button',
        variant: 'secondary',
      });
    }

    return this.processWizardButton(navButtons.back);
  }

  private processNextButton(): ProcessedWizardButton | null {
    const navButtons = this.currentStep.navigationButtons;
    const isLastStep =
      this.currentStepIndex === (this.layout.steps?.length || 0) - 1;

    if (!navButtons?.next) {
      // Default next/submit button
      return this.processWizardButton({
        text: isLastStep ? 'Submit' : 'Next',
        type: isLastStep ? 'submit' : 'button',
        variant: isLastStep ? 'success' : 'primary',
      });
    }

    return this.processWizardButton(navButtons.next);
  }

  private processSkipButton(): ProcessedWizardButton | null {
    if (!this.currentStep.skippable) return null;

    const navButtons = this.currentStep.navigationButtons;
    if (!navButtons?.skip) {
      // Default skip button
      return this.processWizardButton({
        text: 'Skip',
        type: 'button',
        variant: 'link',
      });
    }

    return this.processWizardButton(navButtons.skip);
  }

  private processCustomButtons(): ProcessedWizardButton[] {
    const customButtons = this.currentStep.navigationButtons?.custom || [];

    return customButtons
      .filter((button) => !this.evaluateButtonHidden(button))
      .sort((a, b) => (a.order || 0) - (b.order || 0))
      .map((button) => this.processWizardButton(button));
  }

  private processWizardButton(config: IButtonConfig): ProcessedWizardButton {
    return {
      type: config.type || 'button',
      text: config.text || '',
      icon: config.icon,
      showLeftIcon: !!(config.icon && config.iconPosition === 'left'),
      showRightIcon: !!(
        config.icon &&
        (config.iconPosition === 'right' || !config.iconPosition)
      ),
      style: config.style || {},
      disabled: this.evaluateButtonDisabled(config),
      originalConfig: config,
    };
  }

  private precomputeButtonVariants(): void {
    // Back button variants
    if (this.backButton) {
      const variant = this.backButton.originalConfig.variant || 'secondary';
      this.backButtonVariantPrimary = variant === 'primary';
      this.backButtonVariantSecondary = variant === 'secondary';
      this.backButtonVariantLight = variant === 'light';
    }

    // Next button variants
    if (this.nextButton) {
      const variant = this.nextButton.originalConfig.variant || 'primary';
      this.nextButtonVariantPrimary = variant === 'primary';
      this.nextButtonVariantSuccess = variant === 'success';
      this.nextButtonVariantSecondary = variant === 'secondary';
    }

    // Skip button variants
    if (this.skipButton) {
      const variant = this.skipButton.originalConfig.variant || 'link';
      this.skipButtonVariantSecondary = variant === 'secondary';
      this.skipButtonVariantLight = variant === 'light';
      this.skipButtonVariantLink = variant === 'link';
    }
  }

  private canGoNext(): boolean {
    // TODO: Implement step validation logic
    // Check if current step is valid before allowing next
    return true;
  }

  private canSkip(): boolean {
    // TODO: Implement skip validation logic
    return this.currentStep.optional || this.currentStep.skippable || false;
  }

  private evaluateButtonDisabled(button: IButtonConfig): boolean {
    if (typeof button.disabled === 'boolean') {
      return button.disabled;
    }

    // TODO: Implement condition evaluation
    return false;
  }

  private evaluateButtonHidden(button: IButtonConfig): boolean {
    if (typeof button.hidden === 'boolean') {
      return button.hidden;
    }

    // TODO: Implement condition evaluation
    return false;
  }
}
