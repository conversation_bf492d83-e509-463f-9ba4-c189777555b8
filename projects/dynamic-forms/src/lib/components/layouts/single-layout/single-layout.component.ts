import { Component, Input } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IFormLayout } from '../../../interfaces';

@Component({
  selector: 'lib-single-layout',
  template: `
    <div
      class="flex flex-col gap-6"
      [class]="layout.className"
      [ngStyle]="layout.style || {}"
    >
      <lib-section
        *ngFor="let section of layout.sections"
        [section]="section"
        [form]="form"
      >
      </lib-section>
    </div>
  `,
})
export class SingleLayoutComponent {
  @Input() public layout!: IFormLayout;
  @Input() public form!: FormGroup;
}
