import { Component, Input } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IFormLayout } from '../../../interfaces';

@Component({
  selector: 'lib-tabs-layout',
  template: `
    <div
      class="flex flex-col"
      [ngClass]="[layout.className || '']"
      [ngStyle]="layout.style || {}"
    >
      <div class="flex border-b border-gray-200 mb-6">
        <button
          *ngFor="let tab of layout.tabs; let i = index"
          type="button"
          class="px-4 py-3 border-b-2 border-transparent cursor-pointer flex items-center gap-2 transition-all hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          [ngClass]="{
            'border-blue-500 text-blue-600': i === activeTabIndex,
            'text-gray-500 hover:text-gray-700': i !== activeTabIndex
          }"
          [disabled]="tab.disabled"
          [hidden]="tab.hidden"
          (click)="selectTab(i)"
        >
          <span *ngIf="tab.icon">{{ tab.icon }}</span>
          {{ tab.title }}
          <span
            *ngIf="tab.badge"
            class="bg-red-500 text-white rounded-full px-2 py-0.5 text-xs min-w-[20px] text-center"
          >
            {{ tab.badge }}
          </span>
        </button>
      </div>

      <div class="flex-1">
        <div
          *ngFor="let tab of layout.tabs; let i = index"
          [hidden]="i !== activeTabIndex"
        >
          <lib-section
            *ngFor="let section of tab.sections"
            [section]="section"
            [form]="form"
          >
          </lib-section>
        </div>
      </div>
    </div>
  `,
})
export class TabsLayoutComponent {
  @Input() public layout!: IFormLayout;
  @Input() public form!: FormGroup;

  public activeTabIndex: number = 0;

  public selectTab(index: number): void {
    this.activeTabIndex = index;
  }
}
