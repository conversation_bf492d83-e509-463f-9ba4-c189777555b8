import { Component, Input } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { ISection } from '../../interfaces';

@Component({
  selector: 'lib-section',
  template: `
    <div
      [ngClass]="section.className ?? ''"
      [ngStyle]="section.style ?? {}"
      [hidden]="section.hidden"
    >
      <div *ngIf="section.title || section.description" class="mb-6">
        <h3
          *ngIf="section.title"
          class="flex items-center gap-2 m-0 mb-2 text-xl font-semibold text-gray-900"
        >
          <span *ngIf="section.icon">{{ section.icon }}</span>
          {{ section.title }}
        </h3>
        <p *ngIf="section.description" class="m-0 text-gray-600 text-sm">
          {{ section.description }}
        </p>
      </div>

      <div class="flex flex-col gap-4">
        <lib-row *ngFor="let row of section.rows" [row]="row" [form]="form">
        </lib-row>
      </div>
    </div>
  `,
})
export class SectionComponent {
  @Input() public section!: ISection;
  @Input() public form!: FormGroup;
}
