import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IButtonConfig, IFormConfig } from '../../interfaces';
import {
  FormBuilderService,
  FormContextService,
  FormStateService,
  ConditionalEngineService,
} from '../../services';

export interface ProcessedButton {
  type: string;
  text: string;
  icon?: string;
  showLeftIcon: boolean;
  showRightIcon: boolean;
  style: any;
  disabled: boolean;
  originalConfig: IButtonConfig;
}

@Component({
  selector: 'lib-dynamic-form',
  template: `
    <form
      [formGroup]="form"
      (ngSubmit)="onSubmit()"
      [class]="config.settings?.className"
    >
      <div [libLayoutHost]="config.layout" [form]="form"></div>

      <div
        *ngIf="shouldShowButtons"
        class="mt-8 pt-4 border-t border-gray-200 flex justify-end gap-3"
      >
        <!-- Submit button -->
        <button
          *ngIf="submitButton"
          [type]="submitButton.type"
          [disabled]="submitButton.disabled"
          [ngClass]="[
            submitButtonVariantPrimary && !submitButton.disabled
              ? 'bg-blue-500 hover:bg-blue-600 text-white'
              : '',
            submitButtonVariantSecondary && !submitButton.disabled
              ? 'bg-gray-500 hover:bg-gray-600 text-white'
              : '',
            submitButtonVariantSuccess && !submitButton.disabled
              ? 'bg-green-500 hover:bg-green-600 text-white'
              : '',
            submitButtonVariantDanger && !submitButton.disabled
              ? 'bg-red-500 hover:bg-red-600 text-white'
              : '',
            submitButtonVariantWarning && !submitButton.disabled
              ? 'bg-yellow-500 hover:bg-yellow-600 text-white'
              : '',
            submitButtonVariantInfo && !submitButton.disabled
              ? 'bg-cyan-500 hover:bg-cyan-600 text-white'
              : '',
            submitButtonVariantLight && !submitButton.disabled
              ? 'bg-gray-100 text-gray-900 hover:bg-gray-200'
              : '',
            submitButtonVariantDark && !submitButton.disabled
              ? 'bg-gray-900 text-white hover:bg-gray-800'
              : '',
            submitButtonVariantLink && !submitButton.disabled
              ? 'bg-transparent text-blue-500 hover:text-blue-600 hover:underline'
              : '',

            submitButton.disabled
              ? 'opacity-50 cursor-not-allowed bg-gray-300 text-gray-500'
              : '',

            'px-6 py-2 rounded-md font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed',
            submitButton.originalConfig.fullWidth ? 'w-full' : ''
          ]"
          (click)="onSubmitClick()"
        >
          <span class="inline-flex items-center gap-x-2">
            <span *ngIf="!isSubmitting && submitButton.showLeftIcon">
              {{ submitButton.icon }}
            </span>

            {{
              isSubmitting && submitButton.originalConfig.loadingText
                ? submitButton.originalConfig.loadingText
                : submitButton.text
            }}

            <span *ngIf="!isSubmitting && submitButton.showRightIcon">
              {{ submitButton.icon }}
            </span>

            <span *ngIf="isSubmitting" class="flex items-center">
              <svg
                class="animate-spin h-5 w-5 text-white inline-block"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                ></circle>
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                ></path>
              </svg>
            </span>
          </span>
        </button>

        <!-- Reset button -->
        <button
          *ngIf="resetButton"
          [type]="resetButton.type"
          [disabled]="resetButton.disabled"
          [ngClass]="[
            resetButtonVariantPrimary && !resetButton.disabled
              ? 'bg-blue-500 hover:bg-blue-600 text-white'
              : '',
            resetButtonVariantSecondary && !resetButton.disabled
              ? 'bg-gray-500 hover:bg-gray-600 text-white'
              : '',
            resetButton.disabled
              ? 'opacity-50 cursor-not-allowed bg-gray-300 text-gray-500'
              : '',

            'px-6 py-2 rounded-md font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed',
            resetButton.originalConfig.fullWidth ? 'w-full' : ''
          ]"
          (click)="onResetClick()"
        >
          <span *ngIf="resetButton.showLeftIcon">{{ resetButton.icon }}</span>
          {{ resetButton.text }}
          <span *ngIf="resetButton.showRightIcon">{{ resetButton.icon }}</span>
        </button>

        <!-- Cancel button -->
        <button
          *ngIf="cancelButton"
          [type]="cancelButton.type"
          [disabled]="cancelButton.disabled"
          [ngClass]="[
            cancelButtonVariantLight && !cancelButton.disabled
              ? 'bg-gray-100 text-gray-900 hover:bg-gray-200'
              : '',
            cancelButton.disabled
              ? 'opacity-50 cursor-not-allowed bg-gray-300 text-gray-500'
              : '',

            'px-6 py-2 rounded-md font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed',
            cancelButton.originalConfig.fullWidth ? 'w-full' : ''
          ]"
          (click)="onCancelClick()"
        >
          <span *ngIf="cancelButton.showLeftIcon">{{ cancelButton.icon }}</span>
          {{ cancelButton.text }}
          <span *ngIf="cancelButton.showRightIcon">
            {{ cancelButton.icon }}
          </span>
        </button>

        <!-- Custom buttons -->
        <button
          *ngFor="let button of customButtons"
          [type]="button.type"
          [disabled]="button.disabled"
          [ngClass]="[
            button.originalConfig.variant === 'primary' && !button.disabled
              ? 'bg-blue-500 hover:bg-blue-600 text-white'
              : '',
            button.originalConfig.variant === 'secondary' && !button.disabled
              ? 'bg-gray-500 hover:bg-gray-600 text-white'
              : '',
            button.originalConfig.variant === 'success' && !button.disabled
              ? 'bg-green-500 hover:bg-green-600 text-white'
              : '',
            button.originalConfig.variant === 'danger' && !button.disabled
              ? 'bg-red-500 hover:bg-red-600 text-white'
              : '',
            button.originalConfig.variant === 'warning' && !button.disabled
              ? 'bg-yellow-500 hover:bg-yellow-600 text-white'
              : '',
            button.originalConfig.variant === 'info' && !button.disabled
              ? 'bg-cyan-500 hover:bg-cyan-600 text-white'
              : '',
            button.originalConfig.variant === 'light' && !button.disabled
              ? 'bg-gray-100 text-gray-900 hover:bg-gray-200'
              : '',
            button.originalConfig.variant === 'dark' && !button.disabled
              ? 'bg-gray-900 text-white hover:bg-gray-800'
              : '',
            button.originalConfig.variant === 'link' && !button.disabled
              ? 'bg-transparent text-blue-500 hover:text-blue-600 hover:underline'
              : '',

            button.disabled
              ? 'opacity-50 cursor-not-allowed bg-gray-300 text-gray-500'
              : '',

            'px-6 py-2 rounded-md font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed',
            button.originalConfig.fullWidth ? 'w-full' : ''
          ]"
          (click)="onCustomButtonClick(button)"
        >
          <span *ngIf="button.showLeftIcon">{{ button.icon }}</span>
          {{ button.text }}
          <span *ngIf="button.showRightIcon">{{ button.icon }}</span>
        </button>
      </div>
    </form>
  `,
  providers: [FormContextService, FormStateService],
})
export class DynamicFormComponent implements OnInit, OnDestroy, OnChanges {
  @Input() public config!: IFormConfig;
  @Input() public initialValues?: Record<string, any>;
  @Input() public showSubmitButton: boolean = true;

  @Output() public formSubmit = new EventEmitter<any>();
  @Output() public formChange = new EventEmitter<any>();
  @Output() public formValid = new EventEmitter<boolean>();
  @Output() public buttonClick = new EventEmitter<{
    button: IButtonConfig;
    action: string;
    data?: any;
  }>();

  public form!: FormGroup;
  public isSubmitting: boolean = false;

  public shouldShowButtons: boolean = false;

  public submitButton: ProcessedButton | null = null;
  public resetButton: ProcessedButton | null = null;
  public cancelButton: ProcessedButton | null = null;
  public customButtons: ProcessedButton[] = [];

  // Precomputed variant booleans for submit button
  public submitButtonVariantPrimary = false;
  public submitButtonVariantSecondary = false;
  public submitButtonVariantSuccess = false;
  public submitButtonVariantDanger = false;
  public submitButtonVariantWarning = false;
  public submitButtonVariantInfo = false;
  public submitButtonVariantLight = false;
  public submitButtonVariantDark = false;
  public submitButtonVariantLink = false;

  // Precomputed variant booleans for reset button
  public resetButtonVariantPrimary = false;
  public resetButtonVariantSecondary = false;

  // Precomputed variant booleans for cancel button
  public cancelButtonVariantLight = false;

  public constructor(
    private readonly formBuilder: FormBuilderService,
    private readonly formContext: FormContextService,
    private readonly conditionalEngine: ConditionalEngineService
  ) {}

  public ngOnInit(): void {
    this.buildForm();
    this.formContext.setFormGroup(this.form);
    this.setupConditionals();
    this.setupSubscriptions();
    this.updateButtonStates();
  }

  public ngOnChanges(changes: SimpleChanges): void {
    if (changes['config'] || changes['showSubmitButton']) {
      this.updateButtonStates();
    }
  }

  public ngOnDestroy(): void {
    this.conditionalEngine.cleanup();
  }

  public onSubmit(): void {
    if (this.form.valid) {
      this.isSubmitting = true;
      this.updateButtonStates();
      this.formSubmit.emit(this.form.value);

      setTimeout(() => {
        this.isSubmitting = false;
        this.updateButtonStates(); // Update button states after submission
      }, 1000);
    }
  }

  public onSubmitClick(): void {
    this.onSubmit();
    if (this.submitButton?.originalConfig) {
      this.buttonClick.emit({
        button: this.submitButton.originalConfig,
        action: 'submit',
        data: this.form.value,
      });
    }
  }

  public onResetClick(): void {
    this.formContext.reset(this.initialValues);

    if (this.resetButton?.originalConfig) {
      this.buttonClick.emit({
        button: this.resetButton.originalConfig,
        action: 'reset',
      });
    }
  }

  public onCancelClick(): void {
    if (this.cancelButton?.originalConfig) {
      this.buttonClick.emit({
        button: this.cancelButton.originalConfig,
        action: 'cancel',
      });
    }
  }

  public onCustomButtonClick(button: ProcessedButton): void {
    if (button.originalConfig) {
      this.buttonClick.emit({
        button: button.originalConfig,
        action: 'custom',
        data: this.form.value,
      });
    }
  }

  private updateButtonStates(): void {
    if (!this.form) return;

    const isWizard = this.config.layout.type === 'wizard';

    this.submitButton = this.processSubmitButton();
    this.resetButton = this.processResetButton();
    this.cancelButton = this.processCancelButton();
    this.customButtons = this.processCustomButtons();

    this.shouldShowButtons =
      !isWizard &&
      (!!this.submitButton ||
        !!this.resetButton ||
        !!this.cancelButton ||
        this.customButtons.length > 0);

    this.precomputeButtonVariants();
  }

  private precomputeButtonVariants(): void {
    // Submit button variants
    if (this.submitButton?.originalConfig.variant) {
      this.submitButtonVariantPrimary =
        this.submitButton.originalConfig.variant === 'primary';
      this.submitButtonVariantSecondary =
        this.submitButton.originalConfig.variant === 'secondary';
      this.submitButtonVariantSuccess =
        this.submitButton.originalConfig.variant === 'success';
      this.submitButtonVariantDanger =
        this.submitButton.originalConfig.variant === 'danger';
      this.submitButtonVariantWarning =
        this.submitButton.originalConfig.variant === 'warning';
      this.submitButtonVariantInfo =
        this.submitButton.originalConfig.variant === 'info';
      this.submitButtonVariantLight =
        this.submitButton.originalConfig.variant === 'light';
      this.submitButtonVariantDark =
        this.submitButton.originalConfig.variant === 'dark';
      this.submitButtonVariantLink =
        this.submitButton.originalConfig.variant === 'link';
    } else {
      // Defaults
      this.submitButtonVariantPrimary = true;
      this.submitButtonVariantSecondary = false;
      this.submitButtonVariantSuccess = false;
      this.submitButtonVariantDanger = false;
      this.submitButtonVariantWarning = false;
      this.submitButtonVariantInfo = false;
      this.submitButtonVariantLight = false;
      this.submitButtonVariantDark = false;
      this.submitButtonVariantLink = false;
    }

    // Reset button variants (only primary and secondary considered here)
    if (this.resetButton?.originalConfig.variant) {
      this.resetButtonVariantPrimary =
        this.resetButton.originalConfig.variant === 'primary';
      this.resetButtonVariantSecondary =
        this.resetButton.originalConfig.variant === 'secondary';
    } else {
      this.resetButtonVariantPrimary = false;
      this.resetButtonVariantSecondary = true; // default secondary for reset
    }

    // Cancel button variants (only light considered here)
    if (this.cancelButton?.originalConfig.variant) {
      this.cancelButtonVariantLight =
        this.cancelButton.originalConfig.variant === 'light';
    } else {
      this.cancelButtonVariantLight = true; // default light for cancel
    }
  }

  private processSubmitButton(): ProcessedButton | null {
    if (!this.showSubmitButton) return null;

    const buttons = this.config.buttons;
    if (buttons?.submit === false) return null;

    const defaultSubmit: IButtonConfig = {
      text: 'Submit',
      type: 'submit',
      variant: 'primary',
    };

    const buttonConfig = buttons?.submit
      ? { ...defaultSubmit, ...buttons.submit }
      : defaultSubmit;

    return {
      ...this.processButtonConfig(buttonConfig),
      disabled:
        this.form.invalid ||
        this.isSubmitting ||
        this.evaluateButtonDisabled(buttonConfig),
      originalConfig: buttonConfig,
    };
  }

  private processResetButton(): ProcessedButton | null {
    const buttons = this.config.buttons;
    if (!buttons?.reset) return null;

    const defaultReset: IButtonConfig = {
      text: 'Reset',
      type: 'button',
      variant: 'secondary',
    };

    const buttonConfig = { ...defaultReset, ...buttons.reset };

    return {
      ...this.processButtonConfig(buttonConfig),
      disabled: this.evaluateButtonDisabled(buttonConfig),
      originalConfig: buttonConfig,
    };
  }

  private processCancelButton(): ProcessedButton | null {
    const buttons = this.config.buttons;
    if (!buttons?.cancel) return null;

    const defaultCancel: IButtonConfig = {
      text: 'Cancel',
      type: 'button',
      variant: 'light',
    };

    const buttonConfig = { ...defaultCancel, ...buttons.cancel };

    return {
      ...this.processButtonConfig(buttonConfig),
      disabled: this.evaluateButtonDisabled(buttonConfig),
      originalConfig: buttonConfig,
    };
  }

  private processCustomButtons(): ProcessedButton[] {
    const customButtons = this.config.buttons?.custom || [];

    return customButtons
      .filter((button) => !this.evaluateButtonHidden(button))
      .sort((a, b) => (a.order || 0) - (b.order || 0))
      .map((button) => ({
        ...this.processButtonConfig(button),
        disabled: this.evaluateButtonDisabled(button),
        originalConfig: button,
      }));
  }

  private processButtonConfig(
    button: IButtonConfig
  ): Omit<ProcessedButton, 'disabled' | 'originalConfig'> {
    return {
      type: button.type || 'button',
      text: this.getButtonText(button),
      icon: button.icon,
      showLeftIcon: !!(button.icon && button.iconPosition === 'left'),
      showRightIcon: !!(
        button.icon &&
        (button.iconPosition === 'right' || !button.iconPosition)
      ),
      style: button.style || {},
    };
  }

  private getButtonText(button: IButtonConfig): string {
    if (button.loading && button.loadingText) {
      return button.loadingText;
    }
    return button.text || '';
  }

  private evaluateButtonDisabled(button: IButtonConfig): boolean {
    if (typeof button.disabled === 'boolean') {
      return button.disabled;
    }

    // TODO: Implement condition evaluation when available
    return false;
  }

  private evaluateButtonHidden(button: IButtonConfig): boolean {
    if (typeof button.hidden === 'boolean') {
      return button.hidden;
    }

    // TODO: Implement condition evaluation when available
    return false;
  }

  private buildForm(): void {
    this.form = this.formBuilder.buildForm(this.config);

    if (this.initialValues) {
      this.form.patchValue(this.initialValues);
    }
  }

  private setupConditionals(): void {
    if (this.config.conditionals?.length) {
      this.conditionalEngine.applyRules(this.form, this.config.conditionals);
    }
  }

  private setupSubscriptions(): void {
    this.form.valueChanges.subscribe((value) => {
      this.formChange.emit(value);
      this.updateButtonStates(); // Update buttons when form changes
    });

    this.form.statusChanges.subscribe((status) => {
      this.formValid.emit(status === 'VALID');
      this.updateButtonStates(); // Update buttons when validity changes
    });
  }
}
