// column.component.ts
import { Component, Input, OnInit, HostBinding } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IColumn } from '../../interfaces';

@Component({
  selector: 'lib-column',
  template: `
    <div
      [class]="column.className"
      [ngStyle]="innerStyles"
      [hidden]="column.hidden"
    >
      <div
        *ngFor="let field of column.fields"
        [libFieldHost]="field"
        [form]="form"
      ></div>
    </div>
  `,
  styles: [
    `
      :host {
        /* Default: span 1 column */
        grid-column: span 1;
        min-width: 0;
      }

      /* Responsive grid spans - xs (default) */
      :host[data-xs='1'] {
        grid-column: span 1;
      }
      :host[data-xs='2'] {
        grid-column: span 2;
      }
      :host[data-xs='3'] {
        grid-column: span 3;
      }
      :host[data-xs='4'] {
        grid-column: span 4;
      }
      :host[data-xs='5'] {
        grid-column: span 5;
      }
      :host[data-xs='6'] {
        grid-column: span 6;
      }
      :host[data-xs='7'] {
        grid-column: span 7;
      }
      :host[data-xs='8'] {
        grid-column: span 8;
      }
      :host[data-xs='9'] {
        grid-column: span 9;
      }
      :host[data-xs='10'] {
        grid-column: span 10;
      }
      :host[data-xs='11'] {
        grid-column: span 11;
      }
      :host[data-xs='12'] {
        grid-column: span 12;
      }

      /* sm: 576px and up */
      @media (min-width: 576px) {
        :host[data-sm='1'] {
          grid-column: span 1;
        }
        :host[data-sm='2'] {
          grid-column: span 2;
        }
        :host[data-sm='3'] {
          grid-column: span 3;
        }
        :host[data-sm='4'] {
          grid-column: span 4;
        }
        :host[data-sm='5'] {
          grid-column: span 5;
        }
        :host[data-sm='6'] {
          grid-column: span 6;
        }
        :host[data-sm='7'] {
          grid-column: span 7;
        }
        :host[data-sm='8'] {
          grid-column: span 8;
        }
        :host[data-sm='9'] {
          grid-column: span 9;
        }
        :host[data-sm='10'] {
          grid-column: span 10;
        }
        :host[data-sm='11'] {
          grid-column: span 11;
        }
        :host[data-sm='12'] {
          grid-column: span 12;
        }
      }

      /* md: 768px and up */
      @media (min-width: 768px) {
        :host[data-md='1'] {
          grid-column: span 1;
        }
        :host[data-md='2'] {
          grid-column: span 2;
        }
        :host[data-md='3'] {
          grid-column: span 3;
        }
        :host[data-md='4'] {
          grid-column: span 4;
        }
        :host[data-md='5'] {
          grid-column: span 5;
        }
        :host[data-md='6'] {
          grid-column: span 6;
        }
        :host[data-md='7'] {
          grid-column: span 7;
        }
        :host[data-md='8'] {
          grid-column: span 8;
        }
        :host[data-md='9'] {
          grid-column: span 9;
        }
        :host[data-md='10'] {
          grid-column: span 10;
        }
        :host[data-md='11'] {
          grid-column: span 11;
        }
        :host[data-md='12'] {
          grid-column: span 12;
        }
      }

      /* lg: 992px and up */
      @media (min-width: 992px) {
        :host[data-lg='1'] {
          grid-column: span 1;
        }
        :host[data-lg='2'] {
          grid-column: span 2;
        }
        :host[data-lg='3'] {
          grid-column: span 3;
        }
        :host[data-lg='4'] {
          grid-column: span 4;
        }
        :host[data-lg='5'] {
          grid-column: span 5;
        }
        :host[data-lg='6'] {
          grid-column: span 6;
        }
        :host[data-lg='7'] {
          grid-column: span 7;
        }
        :host[data-lg='8'] {
          grid-column: span 8;
        }
        :host[data-lg='9'] {
          grid-column: span 9;
        }
        :host[data-lg='10'] {
          grid-column: span 10;
        }
        :host[data-lg='11'] {
          grid-column: span 11;
        }
        :host[data-lg='12'] {
          grid-column: span 12;
        }
      }

      /* xl: 1200px and up */
      @media (min-width: 1200px) {
        :host[data-xl='1'] {
          grid-column: span 1;
        }
        :host[data-xl='2'] {
          grid-column: span 2;
        }
        :host[data-xl='3'] {
          grid-column: span 3;
        }
        :host[data-xl='4'] {
          grid-column: span 4;
        }
        :host[data-xl='5'] {
          grid-column: span 5;
        }
        :host[data-xl='6'] {
          grid-column: span 6;
        }
        :host[data-xl='7'] {
          grid-column: span 7;
        }
        :host[data-xl='8'] {
          grid-column: span 8;
        }
        :host[data-xl='9'] {
          grid-column: span 9;
        }
        :host[data-xl='10'] {
          grid-column: span 10;
        }
        :host[data-xl='11'] {
          grid-column: span 11;
        }
        :host[data-xl='12'] {
          grid-column: span 12;
        }
      }

      /* 2xl: 1400px and up */
      @media (min-width: 1400px) {
        :host[data-xxl='1'] {
          grid-column: span 1;
        }
        :host[data-xxl='2'] {
          grid-column: span 2;
        }
        :host[data-xxl='3'] {
          grid-column: span 3;
        }
        :host[data-xxl='4'] {
          grid-column: span 4;
        }
        :host[data-xxl='5'] {
          grid-column: span 5;
        }
        :host[data-xxl='6'] {
          grid-column: span 6;
        }
        :host[data-xxl='7'] {
          grid-column: span 7;
        }
        :host[data-xxl='8'] {
          grid-column: span 8;
        }
        :host[data-xxl='9'] {
          grid-column: span 9;
        }
        :host[data-xxl='10'] {
          grid-column: span 10;
        }
        :host[data-xxl='11'] {
          grid-column: span 11;
        }
        :host[data-xxl='12'] {
          grid-column: span 12;
        }
      }

      /* Offset support */
      :host[data-offset-xs='1'] {
        grid-column-start: 2;
      }
      :host[data-offset-xs='2'] {
        grid-column-start: 3;
      }
      :host[data-offset-xs='3'] {
        grid-column-start: 4;
      }
      /* ... add more offsets as needed */
    `,
  ],
})
export class ColumnComponent implements OnInit {
  @Input() public column!: IColumn;
  @Input() public form!: FormGroup;

  @HostBinding('attr.data-xs') public get dataXs(): number | null {
    return this.getBreakpointValue('xs');
  }

  @HostBinding('attr.data-sm') public get dataSm(): number | null {
    return this.getBreakpointValue('sm');
  }

  @HostBinding('attr.data-md') public get dataMd(): number | null {
    return this.getBreakpointValue('md');
  }

  @HostBinding('attr.data-lg') public get dataLg(): number | null {
    return this.getBreakpointValue('lg');
  }

  @HostBinding('attr.data-xl') public get dataXl(): number | null {
    return this.getBreakpointValue('xl');
  }

  @HostBinding('attr.data-xxl') public get dataXxl(): number | null {
    return this.getBreakpointValue('xxl');
  }

  public innerStyles: any = {};

  public ngOnInit(): void {
    this.buildInnerStyles();
  }

  private getBreakpointValue(breakpoint: string): number | null {
    const { width } = this.column;

    if (typeof width === 'number') {
      return breakpoint === 'xs' ? width : null;
    }

    if (width) {
      // Type-safe way to access responsive width properties
      switch (breakpoint) {
        case 'xs':
          return width.xs || null;
        case 'sm':
          return width.sm || null;
        case 'md':
          return width.md || null;
        case 'lg':
          return width.lg || null;
        case 'xl':
          return width.xl || null;
        case 'xxl':
          return width.xxl || null;
        default:
          return null;
      }
    }

    return null;
  }

  private buildInnerStyles(): void {
    this.innerStyles = {
      ...this.column.style,
      ...(this.column.align && { textAlign: this.column.align }),
    };
  }
}
