import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IField } from '../../../interfaces';
import { FormContextService } from '../../../services';

@Component({
  selector: 'lib-range-field',
  template: `
    <lib-field-wrapper [field]="field" [form]="form">
      <div class="space-y-3 ">
        <!-- Range Input -->
        <div class="relative">
          <input
            type="range"
            [id]="field.id"
            [name]="field.name"
            [value]="value"
            [min]="field.min ?? 0"
            [max]="field.max ?? 100"
            [step]="field.step ?? 1"
            [disabled]="isDisabled"
            class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            [ngClass]="{
              'opacity-50 cursor-not-allowed': isDisabled
            }"
            [ngStyle]="field.style || {}"
            (input)="onInput($event)"
            (change)="onChange($event)"
            (blur)="onBlur()"
          />
        </div>

        <!-- Value Display and Labels -->
        <div class="flex justify-between items-center text-sm">
          <span class="text-gray-500">{{ field.min ?? 0 }}</span>

          <div class="flex items-center space-x-2">
            <span
              *ngIf="field.showValue !== false"
              class="font-medium text-gray-900 bg-gray-100 px-2 py-1 rounded"
            >
              {{ displayValue }}
            </span>
            <span *ngIf="field.unit" class="text-gray-500">
              {{ field.unit }}
            </span>
          </div>

          <span class="text-gray-500">{{ field.max ?? 100 }}</span>
        </div>

        <!-- Custom Labels -->
        <div
          *ngIf="field.rangeLabels?.length"
          class="flex justify-between text-xs text-gray-500"
        >
          <span *ngFor="let label of field.rangeLabels">{{ label }}</span>
        </div>
      </div>
    </lib-field-wrapper>
  `,
  styles: [
    `
      /* Custom slider styling */
      .slider::-webkit-slider-thumb {
        appearance: none;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #3b82f6;
        cursor: pointer;
        border: 2px solid #ffffff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .slider::-webkit-slider-thumb:hover {
        background: #2563eb;
      }

      .slider::-moz-range-thumb {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #3b82f6;
        cursor: pointer;
        border: 2px solid #ffffff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .slider:disabled::-webkit-slider-thumb {
        background: #9ca3af;
        cursor: not-allowed;
      }
    `,
  ],
})
export class RangeFieldComponent implements OnInit {
  @Input() public field!: IField;
  @Input() public form!: FormGroup;

  public value: number = 0;
  public isDisabled: boolean = false;
  public hasError: boolean = false;

  public constructor(private readonly formContext: FormContextService) {}

  public get displayValue(): string {
    if (this.field.valueFormatter) return this.field.valueFormatter(this.value);
    return this.value.toString();
  }

  public ngOnInit(): void {
    this.updateState();
    this.formContext.subscribe(() => this.updateState());
  }

  public onInput(event: Event): void {
    const value = Number((event.target as HTMLInputElement).value);
    this.value = value; // Update local value immediately for smooth UI
    this.formContext.setValue(this.field.name, value);
  }

  public onChange(event: Event): void {
    const value = Number((event.target as HTMLInputElement).value);
    this.formContext.setValue(this.field.name, value);
  }

  public onBlur(): void {
    this.formContext.validateField(this.field.name);
  }

  private updateState(): void {
    this.value =
      this.formContext.getValue(this.field.name) ?? (this.field.min || 0);
    this.isDisabled =
      this.formContext.getFieldProperty(this.field.name, 'disabled') || false;
    this.hasError = !!this.formContext.getError(this.field.name);
  }
}
