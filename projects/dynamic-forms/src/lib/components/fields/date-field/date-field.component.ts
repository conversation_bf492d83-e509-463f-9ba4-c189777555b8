import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IField } from '../../../interfaces';
import { FormContextService } from '../../../services';

@Component({
  selector: 'lib-date-field',
  template: `
    <lib-field-wrapper [field]="field" [form]="form">
      <input
        [id]="field.id"
        [name]="field.name"
        [type]="inputType"
        [value]="value"
        [placeholder]="field.placeholder ?? ''"
        [readonly]="field.readonly"
        [disabled]="isDisabled"
        [attr.min]="field.dateConfig?.minDate ?? null"
        [attr.max]="field.dateConfig?.maxDate ?? null"
        [attr.step]="field.step ?? null"
        class="block w-full px-3 py-2 text-base text-gray-900 bg-white border border-gray-300 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:opacity-100 read-only:bg-gray-50"
        [ngClass]="[
          field.className || '',
          hasError
            ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
            : '',
          !hasError && value
            ? 'border-green-500 focus:ring-green-500 focus:border-green-500'
            : ''
        ]"
        [ngStyle]="field.style || {}"
        (input)="onInput($event)"
        (change)="onChange($event)"
        (blur)="onBlur()"
        (focus)="onFocus()"
      />
    </lib-field-wrapper>
  `,
})
export class DateFieldComponent implements OnInit {
  @Input() public field!: IField;
  @Input() public form!: FormGroup;

  public value: string = '';
  public isDisabled: boolean = false;
  public hasError: boolean = false;
  public inputType: string = 'date';

  public constructor(private readonly formContext: FormContextService) {}

  public ngOnInit(): void {
    this.inputType = this.resolveDateType(this.field.type);
    this.updateState();
    this.formContext.subscribe(() => this.updateState());
  }

  private resolveDateType(type?: string): string {
    const typeMap: { [key: string]: string } = {
      date: 'date',
      datetime: 'datetime-local',
      time: 'time',
      month: 'month',
      week: 'week',
    };
    return type ? typeMap[type] || 'date' : 'date';
  }

  public onInput(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.formContext.setValue(this.field.name, value || null);
  }

  public onChange(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.formContext.setValue(this.field.name, value || null);
  }

  public onBlur(): void {
    this.formContext.validateField(this.field.name);
  }

  public onFocus(): void {
    // Handle focus events if needed
  }

  private updateState(): void {
    this.value = this.formContext.getValue(this.field.name) || '';
    this.isDisabled =
      this.formContext.getFieldProperty(this.field.name, 'disabled') || false;
    this.hasError = !!this.formContext.getError(this.field.name);
  }
}
