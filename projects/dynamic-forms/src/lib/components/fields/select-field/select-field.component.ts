import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IField, IOption } from '../../../interfaces';
import { FormContextService } from '../../../services';

@Component({
  selector: 'lib-select-field',
  template: `
    <lib-field-wrapper [field]="field" [form]="form">
      <select
        [id]="field.id"
        [name]="field.name"
        [disabled]="isDisabled"
        [multiple]="field.multiple"
        class="block w-full px-3 py-2 text-base text-gray-900 bg-white border border-gray-300 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50"
        [ngClass]="[
          field.className || '',
          hasError
            ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
            : '',
          !hasError && value
            ? 'border-green-500 focus:ring-green-500 focus:border-green-500'
            : ''
        ]"
        [ngStyle]="field.style ?? null"
        (change)="onChange($event)"
        (blur)="onBlur()"
      >
        <option *ngIf="!field.multiple" value="" class="text-gray-500">
          {{ field.placeholder || 'Select...' }}
        </option>

        <option
          *ngFor="let option of options"
          [value]="option.value"
          [disabled]="option.disabled"
          [selected]="isSelected(option.value)"
          class="text-gray-900"
        >
          {{ option.label }}
        </option>
      </select>
    </lib-field-wrapper>
  `,
})
export class SelectFieldComponent implements OnInit {
  @Input() public field!: IField;
  @Input() public form!: FormGroup;

  public value: any = null;
  public options: IOption[] = [];
  public isDisabled: boolean = false;
  public hasError: boolean = false;

  public constructor(private readonly formContext: FormContextService) {}

  public ngOnInit(): void {
    this.loadOptions();
    this.updateState();
    this.formContext.subscribe(() => this.updateState());
  }

  public onChange(event: Event): void {
    const select = event.target as HTMLSelectElement;
    let value;

    if (this.field.multiple) {
      value = Array.from(select.selectedOptions).map((option) => option.value);
    } else {
      value = select.value || null;
    }

    this.formContext.setValue(this.field.name, value);
  }

  public isSelected(optionValue: any): boolean {
    if (this.field.multiple) {
      return Array.isArray(this.value) && this.value.includes(optionValue);
    }
    return this.value === optionValue;
  }

  public onBlur(): void {
    this.formContext.validateField(this.field.name);
  }

  private loadOptions(): void {
    if (Array.isArray(this.field.options)) {
      this.options = this.field.options;
    } else if (this.field.options?.data) {
      this.options = this.field.options.data;
    }
  }

  private updateState(): void {
    this.value = this.formContext.getValue(this.field.name);
    this.isDisabled =
      this.formContext.getFieldProperty(this.field.name, 'disabled') || false;
    this.hasError = !!this.formContext.getError(this.field.name);
  }
}
