import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IField } from '../../../interfaces';
import { FormContextService } from '../../../services';

@Component({
  selector: 'lib-number-field',
  template: `
    <lib-field-wrapper [field]="field" [form]="form">
      <input
        type="number"
        [id]="field.id"
        [name]="field.name"
        [placeholder]="field.placeholder"
        [readonly]="field.readonly"
        [disabled]="isDisabled"
        [value]="value"
        [min]="field.min"
        [max]="field.max"
        [step]="field.step"
        class="block w-full px-3 py-2 text-base text-gray-900 bg-white border border-gray-300 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:opacity-100 read-only:bg-gray-50"
        [ngClass]="[
          field.className || '',
          hasError
            ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
            : '',
          !hasError && value !== null
            ? 'border-green-500 focus:ring-green-500 focus:border-green-500'
            : ''
        ]"
        [ngStyle]="field.style ?? null"
        (input)="onInput($event)"
        (blur)="onBlur()"
      />
    </lib-field-wrapper>
  `,
})
export class NumberFieldComponent implements OnInit {
  @Input() public field!: IField;
  @Input() public form!: FormGroup;

  public value: number | null = null;
  public isDisabled: boolean = false;
  public hasError: boolean = false;

  public constructor(private readonly formContext: FormContextService) {}

  public ngOnInit(): void {
    this.updateState();
    this.formContext.subscribe(() => this.updateState());
  }

  public onInput(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    const numberValue = value ? Number(value) : null;
    this.formContext.setValue(this.field.name, numberValue);
  }

  public onBlur(): void {
    this.formContext.validateField(this.field.name);
  }

  private updateState(): void {
    this.value = this.formContext.getValue(this.field.name);
    this.isDisabled =
      this.formContext.getFieldProperty(this.field.name, 'disabled') || false;
    this.hasError = !!this.formContext.getError(this.field.name);
  }
}
