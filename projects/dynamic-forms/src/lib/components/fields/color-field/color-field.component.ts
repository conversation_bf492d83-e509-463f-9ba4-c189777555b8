import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IField } from '../../../interfaces';
import { FormContextService } from '../../../services';
import { colord } from 'colord';

type ColorFormat = 'hex' | 'rgb' | 'hsl';

@Component({
  selector: 'lib-color-field',
  template: `
    <lib-field-wrapper [field]="field" [form]="form">
      <div class="space-y-3">
        <div class="flex items-center space-x-3">
          <!-- Color Picker Input -->
          <div class="relative">
            <input
              type="color"
              [id]="field.id"
              [name]="field.name"
              [value]="value | execute : getOnlyValidColor"
              [disabled]="isDisabled"
              class="sr-only"
              (input)="onInput($event)"
              (change)="onChange($event)"
              (blur)="onBlur()"
              #colorInput
            />

            <!-- Color Preview Button -->
            <button
              type="button"
              class="w-12 h-10 rounded-md border-2 border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
              [style.background-color]="value"
              [disabled]="isDisabled"
              [ngClass]="{
                'opacity-50 cursor-not-allowed': isDisabled,
                'border-red-500': hasError
              }"
              (click)="colorInput.click()"
            ></button>
          </div>

          <!-- Text Input for Manual Entry -->
          <div class="flex-1">
            <input
              type="text"
              [value]="value"
              [placeholder]="field.placeholder || '#000000'"
              [disabled]="isDisabled"
              [readonly]="field.readonly"
              class="block w-full px-3 py-2 text-base text-gray-900 bg-white border border-gray-300 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 font-mono"
              [ngClass]="{
                'border-red-500 focus:ring-red-500 focus:border-red-500':
                  hasError,
                'border-green-500 focus:ring-green-500 focus:border-green-500':
                  !hasError && isValueValid
              }"
              (input)="onTextInput($event)"
              (blur)="onBlur()"
            />
          </div>

          <!-- Format Toggle -->
          <button
            *ngIf="field.allowFormatToggle !== false"
            type="button"
            class="px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            [disabled]="isDisabled"
            (click)="toggleFormat()"
          >
            {{ currentFormat | uppercase }}
          </button>
        </div>

        <!-- Preset Colors -->
        <div *ngIf="presetColors.length">
          <!-- <div class="text-sm font-medium text-gray-700 mb-2">Preset Colors</div> -->
          <div class="flex flex-wrap gap-2">
            <button
              *ngFor="let preset of presetColors"
              type="button"
              class="w-8 h-8 rounded border-2 border-gray-300 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
              [style.background-color]="preset.value"
              [title]="preset.label"
              [ngClass]="{
                'ring-2 ring-blue-500 border-blue-500': value === preset.value
              }"
              (click)="selectPreset(preset.value)"
            ></button>
          </div>
        </div>
      </div>
    </lib-field-wrapper>
  `,
})
export class ColorFieldComponent implements OnInit {
  @Input() public field!: IField;
  @Input() public form!: FormGroup;

  public value: string = '#000000';
  public isDisabled: boolean = false;
  public hasError: boolean = false;
  public currentFormat: ColorFormat = 'hex';
  public presetColors: Array<{ label: string; value: string }> = [];
  public isValueValid: boolean = false;

  public constructor(private readonly formContext: FormContextService) {}

  public ngOnInit(): void {
    this.loadPresetColors();
    this.updateState();
    this.formContext.subscribe(() => this.updateState());
  }

  public onInput(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.setValueAndValidate(value);
  }

  public onTextInput(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.setValueAndValidate(value);
  }

  public onChange(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.formContext.setValue(this.field.name, value);
    this.formContext.validateField(this.field.name);
    this.updateIsValueValid(value);
  }

  public onBlur(): void {
    this.formContext.validateField(this.field.name);
  }

  public selectPreset(color: string): void {
    this.setValueAndValidate(color);
  }

  public toggleFormat(): void {
    const formats: Array<ColorFormat> = ['hex', 'rgb', 'hsl'];
    const currentIndex = formats.indexOf(this.currentFormat);
    this.currentFormat = formats[(currentIndex + 1) % formats.length];
    const converted = this.convertColor(this.value, this.currentFormat);
    this.setValueAndValidate(converted);
  }

  public getOnlyValidColor(color: string): string {
    return colord(color).isValid() ? colord(color).toHex() : '#000000';
  }

  private setValueAndValidate(value: string): void {
    this.value = value;
    this.formContext.setValue(this.field.name, value);
    this.formContext.validateField(this.field.name);
    this.updateIsValueValid(value);
  }

  private updateIsValueValid(value: string): void {
    this.isValueValid = this.isValidColor(value);
  }

  private isValidColor(color: string): boolean {
    return colord(color).isValid();
  }

  private convertColor(color: string, format: ColorFormat): string {
    try {
      const colorObj = colord(color);
      if (!colorObj.isValid()) return '#000000'; // Fallback for invalid colors

      switch (format) {
        case 'hex':
          return colorObj.toHex();
        case 'rgb':
          return colorObj.toRgbString();
        case 'hsl':
          return colorObj.toHslString();
        default:
          return color;
      }
    } catch {
      return '#000000'; // Fallback for any errors
    }
  }

  private loadPresetColors(): void {
    this.presetColors = this.field.presetColors || [
      { label: 'Black', value: '#000000' },
      { label: 'White', value: '#ffffff' },
      { label: 'Red', value: '#ef4444' },
      { label: 'Green', value: '#10b981' },
      { label: 'Blue', value: '#3b82f6' },
      { label: 'Yellow', value: '#f59e0b' },
      { label: 'Purple', value: '#8b5cf6' },
      { label: 'Pink', value: '#ec4899' },
    ];
  }

  private updateState(): void {
    this.value = this.formContext.getValue(this.field.name);
    this.isDisabled =
      this.formContext.getFieldProperty(this.field.name, 'disabled') || false;
    this.hasError = !!this.formContext.getError(this.field.name);
    this.updateIsValueValid(this.value);
  }
}
