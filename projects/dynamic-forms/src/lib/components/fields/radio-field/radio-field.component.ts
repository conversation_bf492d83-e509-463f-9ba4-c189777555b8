import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IField, IOption } from '../../../interfaces';
import { FormContextService } from '../../../services';

@Component({
  selector: 'lib-radio-field',
  template: `
    <lib-field-wrapper [field]="field" [form]="form">
      <div
        class="space-y-2"
        [ngClass]="{
          'flex flex-wrap gap-4': field.layout === 'horizontal',
          'space-y-2': field.layout !== 'horizontal'
        }"
      >
        <label
          *ngFor="let option of options; trackBy: trackOption"
          class="flex items-center cursor-pointer select-none"
          [class.opacity-50]="option.disabled"
        >
          <div class="relative">
            <input
              type="radio"
              [name]="field.name"
              [value]="option.value"
              [checked]="isSelected(option.value)"
              [disabled]="isDisabled || option.disabled"
              class="sr-only"
              (change)="onChange(option.value)"
              (blur)="onBlur()"
            />
            <div
              class="w-5 h-5 border-2 rounded-full flex items-center justify-center transition-colors"
              [ngClass]="{
                'border-blue-500': isSelected(option.value),
                'border-gray-300': !isSelected(option.value)
              }"
              [class.bg-blue-500]="isSelected(option.value)"
              [class.bg-white]="!isSelected(option.value)"
              [class.bg-gray-50]="isDisabled"
            >
              <div
                *ngIf="isSelected(option.value)"
                class="w-3 h-3 bg-white rounded-full"
              ></div>
            </div>
          </div>
          <div class="ml-3">
            <span class="text-sm font-medium text-gray-900">
              {{ option.label }}
            </span>
            <p *ngIf="option.description" class="text-sm text-gray-500">
              {{ option.description }}
            </p>
          </div>
        </label>
      </div>
    </lib-field-wrapper>
  `,
})
export class RadioFieldComponent implements OnInit {
  @Input() public field!: IField;
  @Input() public form!: FormGroup;

  public value: any = null;
  public options: IOption[] = [];
  public isDisabled: boolean = false;
  public hasError: boolean = false;

  public constructor(private readonly formContext: FormContextService) {}

  public ngOnInit(): void {
    this.loadOptions();
    this.updateState();
    this.formContext.subscribe(() => this.updateState());
  }

  public onChange(optionValue: any): void {
    this.formContext.setValue(this.field.name, optionValue);
  }

  public isSelected(optionValue: any): boolean {
    return this.value === optionValue;
  }

  public onBlur(): void {
    this.formContext.validateField(this.field.name);
  }

  public trackOption(index: number, option: IOption): any {
    return option.value;
  }

  private loadOptions(): void {
    if (Array.isArray(this.field.options)) {
      this.options = this.field.options;
    } else if (this.field.options?.data) {
      this.options = this.field.options.data;
    }
  }

  private updateState(): void {
    this.value = this.formContext.getValue(this.field.name);
    this.isDisabled =
      this.formContext.getFieldProperty(this.field.name, 'disabled') || false;
    this.hasError = !!this.formContext.getError(this.field.name);
  }
}
