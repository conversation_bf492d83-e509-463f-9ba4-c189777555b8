import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IField } from '../../../interfaces';
import { FormContextService } from '../../../services';

@Component({
  selector: 'lib-text-field',
  template: `
    <lib-field-wrapper [field]="field" [form]="form">
      <input
        [id]="field.id"
        [name]="field.name"
        [type]="inputType"
        [value]="value"
        [placeholder]="field.placeholder ?? ''"
        [readonly]="field.readonly"
        [disabled]="isDisabled"
        [attr.minlength]="field.minLength ?? null"
        [attr.maxlength]="field.maxLength ?? null"
        [attr.pattern]="field.pattern ?? null"
        [attr.autocomplete]="field.autocomplete ?? null"
        [attr.spellcheck]="field.spellcheck ?? null"
        class="block w-full px-3 py-2 text-base text-gray-900 bg-white border border-gray-300 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:opacity-100 read-only:bg-gray-50"
        [ngClass]="[
          field.className || '',
          hasError
            ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
            : '',
          !hasError && value
            ? 'border-green-500 focus:ring-green-500 focus:border-green-500'
            : '',
          field.prefix ? 'rounded-l-none' : '',
          field.suffix ? 'rounded-r-none' : ''
        ]"
        [ngStyle]="field.style || {}"
        (input)="onInput($event)"
        (blur)="onBlur()"
        (focus)="onFocus()"
      />
    </lib-field-wrapper>
  `,
})
export class TextFieldComponent implements OnInit {
  @Input() public field!: IField;
  @Input() public form!: FormGroup;

  public value: any = '';
  public isDisabled: boolean = false;
  public hasError: boolean = false;
  public inputType: string = 'text';

  public constructor(private readonly formContext: FormContextService) {}

  public ngOnInit(): void {
    this.inputType = this.resolveInputType(this.field.type);
    this.updateState();
    this.formContext.subscribe(() => this.updateState());
  }

  private resolveInputType(type?: string): string {
    const typeMap: { [key: string]: string } = {
      text: 'text',
      email: 'email',
      password: 'password',
      tel: 'tel',
      url: 'url',
      search: 'search',
    };
    return type ? typeMap[type] || 'text' : 'text';
  }

  public onInput(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.formContext.setValue(this.field.name, value);
  }

  public onBlur(): void {
    this.formContext.validateField(this.field.name);
  }

  public onFocus(): void {
    // Handle focus events if needed
  }

  private updateState(): void {
    this.value = this.formContext.getValue(this.field.name) || '';
    this.isDisabled =
      this.formContext.getFieldProperty(this.field.name, 'disabled') || false;
    this.hasError = !!this.formContext.getError(this.field.name);
  }
}
