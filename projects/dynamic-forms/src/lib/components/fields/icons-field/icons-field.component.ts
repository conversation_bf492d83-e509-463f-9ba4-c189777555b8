import {
  Component,
  Input,
  OnInit,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IField, IIconOption, IIconFieldConfig } from '../../../interfaces';
import { FormContextService } from '../../../services';

@Component({
  selector: 'lib-icons-field',
  template: `
    <lib-field-wrapper
      [field]="field"
      [form]="form"
      [class]="iconConfig.className"
    >
      <!-- Search Input -->
      <div *ngIf="iconConfig.searchable" class="mb-3">
        <input
          type="text"
          [(ngModel)]="searchTerm"
          (input)="onSearchTermChange()"
          [placeholder]="field.placeholder || 'Search icons...'"
          class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <!-- Category Filter -->
      <div
        *ngIf="categories.length > 1 && iconConfig.showCategories"
        class="mb-3"
      >
        <div class="flex flex-wrap gap-2">
          <button
            *ngFor="let category of categories"
            type="button"
            (click)="onCategorySelect(category)"
            [class.bg-blue-500]="selectedCategory === category"
            [class.text-white]="selectedCategory === category"
            [class.bg-gray-100]="selectedCategory !== category"
            class="px-3 py-1 text-sm rounded-full transition-colors hover:bg-blue-400"
          >
            {{ category }}
          </button>
        </div>
      </div>

      <!-- Icons Grid/List -->
      <div [ngClass]="layoutClass" [ngStyle]="field.style || {}">
        <div
          *ngFor="let icon of filteredIcons"
          (click)="toggleIcon(icon)"
          [title]="icon.label"
          [class.ring-2]="icon.selected"
          [class.ring-blue-500]="icon.selected"
          [class.bg-blue-50]="icon.selected"
          [class.opacity-50]="icon.disabled"
          [class.cursor-not-allowed]="icon.disabled"
          [class]="iconConfig.itemClassName || ''"
          class="relative flex items-center justify-center p-3 border border-gray-200 rounded-lg cursor-pointer transition-all hover:bg-gray-50 hover:border-gray-300"
        >
          <ng-container [ngSwitch]="icon.type">
            <i
              *ngSwitchCase="'font'"
              [class]="icon.iconClass"
              [ngStyle]="icon.style"
            ></i>
            <div
              *ngSwitchCase="'svg'"
              [innerHTML]="icon.svg | sanitize"
              class="w-full h-full"
              [ngStyle]="icon.style"
            ></div>
            <img
              *ngSwitchCase="'url'"
              [src]="icon.iconUrl"
              [alt]="icon.label"
              class="w-full h-full object-contain"
              [ngStyle]="icon.style"
            />
            <span
              *ngSwitchCase="'emoji'"
              class="text-2xl"
              [ngStyle]="icon.style"
            >
              {{ icon.emoji }}
            </span>
            <span *ngSwitchDefault class="text-2xl" [ngStyle]="icon.style">
              {{ icon.value }}
            </span>
          </ng-container>

          <!-- Selection Indicator -->
          <div
            *ngIf="icon.selected"
            class="absolute top-1 right-1 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center"
          >
            <svg
              class="w-3 h-3 text-white"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clip-rule="evenodd"
              />
            </svg>
          </div>

          <!-- Tooltip -->
          <div
            *ngIf="field.tooltip"
            class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-800 rounded opacity-0 pointer-events-none transition-opacity group-hover:opacity-100"
          >
            {{ field.tooltip }}
          </div>
        </div>
      </div>

      <!-- Selected Icons Display -->
      <div
        *ngIf="iconConfig.showSelected && selectedIcons.length > 0"
        class="mt-4"
      >
        <div class="text-sm text-gray-600 mb-2">
          Selected: {{ selectedIcons.length }}
          {{ field.maxSelection ? '/' + field.maxSelection : '' }}
        </div>
        <div class="flex flex-wrap gap-2">
          <div
            *ngFor="let value of selectedIcons"
            class="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 rounded-full"
          >
            <span class="text-sm">{{ iconLabels[value] || value }}</span>
            <button
              type="button"
              (click)="removeIcon(value)"
              class="w-4 h-4 flex items-center justify-center text-blue-600 hover:text-blue-800"
              aria-label="Remove icon"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                stroke-width="2"
                class="w-3 h-3"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div *ngIf="hasError" class="mt-2 text-sm text-red-600">
        {{ errorMessage }}
      </div>
    </lib-field-wrapper>
  `,
  styles: [
    `
      :host {
        display: block;
      }
    `,
  ],
})
export class IconsFieldComponent implements OnInit, OnChanges {
  @Input() public field!: IField;
  @Input() public form!: FormGroup;

  public iconConfig!: IIconFieldConfig;
  public filteredIcons: (IIconOption & {
    selected?: boolean;
    type?: string;
    style?: any;
  })[] = [];
  public selectedIcons: any[] = [];
  public iconLabels: Record<string, string> = {};
  public categories: string[] = [];
  public selectedCategory = 'All';
  public searchTerm = '';
  public layoutClass = '';
  public hasError = false;
  public errorMessage = '';
  public isDisabled = false;

  private readonly DEFAULT_LAYOUT = 'grid';
  private readonly DEFAULT_ITEMS_PER_ROW: IIconFieldConfig['itemsPerRow'] = 6;
  private readonly DEFAULT_SIZE: Required<IIconFieldConfig>['size'] = 'medium';

  constructor(public readonly formContext: FormContextService) {}

  ngOnInit(): void {
    this.initConfig();
    this.loadIcons();
    this.updateState();
    this.formContext.subscribe(() => this.updateState());
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['field']) {
      this.initConfig();
      this.loadIcons();
      this.updateState();
    }
  }

  private initConfig(): void {
    this.iconConfig = {
      options: [],
      layout: this.DEFAULT_LAYOUT,
      itemsPerRow: this.DEFAULT_ITEMS_PER_ROW,
      size: this.DEFAULT_SIZE,
      searchable: true,
      showCategories: true,
      showSelected: true,
      className: '',
      itemClassName: '',
      ...this.field.iconConfig,
    };

    this.layoutClass =
      this.iconConfig.layout === 'list'
        ? 'flex flex-col gap-2'
        : `grid gap-2 ${this.getGridColsClass(this.iconConfig.itemsPerRow)}`;
  }

  private loadIcons(): void {
    const icons = this.iconConfig.options || [];
    const sizeMap: Record<Required<IIconFieldConfig>['size'], string> = {
      small: '1rem',
      medium: '1.5rem',
      large: '2rem',
    };

    this.categories = [
      'All',
      ...Array.from(
        new Set(icons.map((i) => i.category).filter((c): c is string => !!c))
      ),
    ];

    this.iconLabels = {};
    icons.forEach((i) => (this.iconLabels[i.value] = i.label));

    this.filteredIcons = icons.map((icon) => ({
      ...icon,
      type: this.detectIconType(icon),
      style: {
        fontSize: sizeMap[this.iconConfig.size || this.DEFAULT_SIZE],
        color: icon.color || 'currentColor',
        ...this.field.style,
      },
      selected: false,
    }));
  }

  private detectIconType(icon: IIconOption): string {
    if (icon.iconClass) return 'font';
    if (icon.svg) return 'svg';
    if (icon.iconUrl) return 'url';
    if (icon.emoji) return 'emoji';
    return 'default';
  }

  private getGridColsClass(count?: IIconFieldConfig['itemsPerRow']): string {
    switch (count) {
      case 4:
        return 'grid-cols-4';
      case 5:
        return 'grid-cols-5';
      case 6:
        return 'grid-cols-6';
      case 8:
        return 'grid-cols-8';
      case 10:
        return 'grid-cols-10';
      default:
        return 'grid-cols-6';
    }
  }

  public onSearchTermChange(): void {
    this.applyFilters();
  }

  public onCategorySelect(category: string): void {
    this.selectedCategory = category;
    this.applyFilters();
  }

  private applyFilters(): void {
    let icons = [...this.iconConfig.options];

    if (this.selectedCategory !== 'All') {
      icons = icons.filter((icon) => icon.category === this.selectedCategory);
    }

    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      icons = icons.filter(
        (icon) =>
          icon.label.toLowerCase().includes(term) ||
          icon.value.toString().toLowerCase().includes(term) ||
          icon.category?.toLowerCase().includes(term) ||
          icon.tags?.some((tag) => tag.toLowerCase().includes(term)) ||
          icon.keywords?.some((keyword) => keyword.toLowerCase().includes(term))
      );
    }

    const sizeMap: Record<Required<IIconFieldConfig>['size'], string> = {
      small: '1rem',
      medium: '1.5rem',
      large: '2rem',
    };

    this.filteredIcons = icons.map((icon) => ({
      ...icon,
      type: this.detectIconType(icon),
      style: {
        fontSize: sizeMap[this.iconConfig.size || this.DEFAULT_SIZE],
        color: icon.color || 'currentColor',
        ...this.field.style,
      },
      selected: this.selectedIcons.includes(icon.value),
    }));
  }

  public toggleIcon(icon: IIconOption): void {
    if (icon.disabled || this.isDisabled) return;

    const value = icon.value;
    if (this.field.multiple) {
      const idx = this.selectedIcons.indexOf(value);
      if (idx > -1) this.selectedIcons.splice(idx, 1);
      else if (
        !this.field.maxSelection ||
        this.selectedIcons.length < this.field.maxSelection
      ) {
        this.selectedIcons.push(value);
      }
      this.formContext.setValue(this.field.name, [...this.selectedIcons]);
    } else if (this.selectedIcons[0] === value) {
      this.selectedIcons = [];
      this.formContext.setValue(this.field.name, null);
    } else {
      this.selectedIcons = [value];
      this.formContext.setValue(this.field.name, value);
    }
    this.applyFilters();
  }

  public removeIcon(value: any): void {
    const idx = this.selectedIcons.indexOf(value);
    if (idx > -1) {
      this.selectedIcons.splice(idx, 1);
      this.formContext.setValue(
        this.field.name,
        this.field.multiple ? [...this.selectedIcons] : null
      );
    }
    this.applyFilters();
  }

  private updateState(): void {
    const val = this.formContext.getValue(this.field.name);
    this.selectedIcons = this.field.multiple
      ? Array.isArray(val)
        ? val
        : []
      : val
      ? [val]
      : [];

    this.isDisabled = !!this.formContext.getFieldProperty(
      this.field.name,
      'disabled'
    );
    this.hasError = !!this.formContext.getError(this.field.name);
    this.errorMessage = this.formContext.getError(this.field.name) || '';

    this.applyFilters();
  }
}
