import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IField } from '../../../interfaces';
import { FormContextService } from '../../../services';

@Component({
  selector: 'lib-toggle-field',
  template: `
    <lib-field-wrapper [field]="field" [form]="form">
      <label class="flex items-center cursor-pointer">
        <div class="relative">
          <input
            type="checkbox"
            [id]="field.id"
            [name]="field.name"
            [disabled]="isDisabled"
            [checked]="value"
            class="sr-only"
            (change)="onChange($event)"
            (blur)="onBlur()"
          />

          <!-- Toggle Background -->
          <div
            class="w-10 h-6 rounded-full transition-colors duration-200 ease-in-out"
            [ngClass]="{
              'bg-blue-500': value && !isDisabled,
              'bg-gray-200': !value && !isDisabled,
              'bg-gray-100': isDisabled
            }"
          >
            <!-- Toggle Circle -->
            <div
              class="w-4 h-4 bg-white rounded-full shadow-md transform transition-transform duration-200 ease-in-out absolute top-1"
              [ngClass]="{
                'translate-x-5': value,
                'translate-x-1': !value
              }"
            ></div>
          </div>
        </div>

        <div *ngIf="field.label || field.description" class="ml-3">
          <span *ngIf="field.label" class="text-sm font-medium text-gray-900">{{
            field.label
          }}</span>
          <p *ngIf="field.description" class="text-sm text-gray-500">
            {{ field.description }}
          </p>
        </div>
      </label>
    </lib-field-wrapper>
  `,
})
export class ToggleFieldComponent implements OnInit {
  @Input() public field!: IField;
  @Input() public form!: FormGroup;

  public value: boolean = false;
  public isDisabled: boolean = false;
  public hasError: boolean = false;

  public constructor(private readonly formContext: FormContextService) {}

  public ngOnInit(): void {
    this.updateState();
    this.formContext.subscribe(() => this.updateState());
  }

  public onChange(event: Event): void {
    const checked = (event.target as HTMLInputElement).checked;
    this.formContext.setValue(this.field.name, checked);
  }

  public onBlur(): void {
    this.formContext.validateField(this.field.name);
  }

  private updateState(): void {
    this.value = this.formContext.getValue(this.field.name) || false;
    this.isDisabled =
      this.formContext.getFieldProperty(this.field.name, 'disabled') || false;
    this.hasError = !!this.formContext.getError(this.field.name);
  }
}
