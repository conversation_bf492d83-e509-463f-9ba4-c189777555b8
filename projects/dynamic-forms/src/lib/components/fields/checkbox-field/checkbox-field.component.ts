import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IField } from '../../../interfaces';
import { FormContextService } from '../../../services';

@Component({
  selector: 'lib-checkbox-field',
  template: `
    <lib-field-wrapper [field]="field" [form]="form">
      <label class="flex items-center cursor-pointer select-none">
        <div class="relative">
          <input
            type="checkbox"
            [id]="field.id"
            [name]="field.name"
            [disabled]="isDisabled"
            [checked]="value"
            class="sr-only"
            (change)="onChange($event)"
            (blur)="onBlur()"
          />
          <div
            class="w-5 h-5 border-2 rounded transition-all relative"
            [ngClass]="{
              'bg-blue-500 border-blue-500': value,
              'bg-white border-gray-300': !value && !isDisabled,
              'bg-gray-50 cursor-not-allowed': isDisabled
            }"
          >
            <svg
              *ngIf="value"
              class="w-3 h-3 text-white absolute top-0.5 left-0.5"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clip-rule="evenodd"
              ></path>
            </svg>
          </div>
        </div>
        <span *ngIf="field.label" class="ml-2 text-sm text-gray-900">
          {{ field.label }}
        </span>
      </label>
    </lib-field-wrapper>
  `,
})
export class CheckboxFieldComponent implements OnInit, OnDestroy {
  @Input() public field!: IField;
  @Input() public form!: FormGroup;

  public value: boolean = false;
  public isDisabled: boolean = false;

  private subscription?: () => void;

  public constructor(private readonly formContext: FormContextService) {}

  public ngOnInit(): void {
    this.updateState();
    this.subscription = this.formContext.subscribe(() => this.updateState());
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription();
  }

  public onChange(event: Event): void {
    const checked = (event.target as HTMLInputElement).checked;
    this.formContext.setValue(this.field.name, checked);
  }

  public onBlur(): void {
    this.formContext.validateField(this.field.name);
  }

  private updateState(): void {
    this.value = this.formContext.getValue(this.field.name) || false;
    this.isDisabled =
      this.formContext.getFieldProperty(this.field.name, 'disabled') || false;
  }
}
