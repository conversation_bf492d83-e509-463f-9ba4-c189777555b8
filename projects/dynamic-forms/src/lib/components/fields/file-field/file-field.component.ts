import { Component, Input, OnInit, ViewChild, ElementRef } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IField } from '../../../interfaces';
import { FormContextService } from '../../../services';
import { fileSizeFormatter } from 'core';

@Component({
  selector: 'lib-file-field',
  template: `
    <lib-field-wrapper [field]="field" [form]="form">
      <div class="space-y-3">
        <!-- File Input -->
        <div
          class="border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-gray-400 transition-colors"
          [ngClass]="{
            'border-blue-500 bg-blue-50': isDragOver,
            'border-red-500': hasError,
            'opacity-50 cursor-not-allowed': isDisabled
          }"
          (dragover)="onDragOver($event)"
          (dragleave)="onDragLeave($event)"
          (drop)="onDrop($event)"
        >
          <input
            #fileInput
            type="file"
            [id]="field.id"
            [name]="field.name"
            [multiple]="field.fileConfig?.multiple"
            [accept]="acceptTypes"
            [disabled]="isDisabled"
            class="hidden"
            (change)="onFileSelect($event)"
          />

          <div *ngIf="selectedFiles.length === 0">
            <svg
              class="mx-auto h-12 w-12 text-gray-400"
              stroke="currentColor"
              fill="none"
              viewBox="0 0 48 48"
            >
              <path
                d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <div class="mt-4 text-center">
              <button
                type="button"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 disabled:opacity-50"
                [disabled]="isDisabled"
                (click)="fileInput.click()"
              >
                Choose {{ field.fileConfig?.multiple ? 'files' : 'file' }}
              </button>
              <p class="mt-2 text-sm text-gray-500">
                or drag and drop
                {{ field.fileConfig?.multiple ? 'files' : 'a file' }} here
              </p>
              <p *ngIf="acceptTypes" class="text-xs text-gray-400 mt-1">
                {{ acceptTypes }}
              </p>
            </div>
          </div>

          <!-- Selected Files -->
          <div *ngIf="selectedFiles.length > 0" class="space-y-2">
            <div
              *ngFor="let file of selectedFiles; trackBy: trackFile"
              class="flex items-center justify-between p-2 bg-gray-50 rounded border"
            >
              <div class="flex items-center space-x-3 min-w-0">
                <div class="flex-shrink-0">
                  <svg
                    class="h-5 w-5 text-gray-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </div>
                <div class="flex-1 min-w-0 overflow-hidden" [title]="file.name">
                  <p
                    class="max-w-full text-sm font-medium text-gray-900 truncate"
                  >
                    {{ file.name }}
                  </p>
                  <span class="text-xs text-gray-500">
                    {{ file.size | fileSizeFormatter }}
                  </span>
                </div>
              </div>
              <button
                type="button"
                class="ml-3 text-gray-400 hover:text-red-500"
                (click)="removeFile(file)"
                [disabled]="isDisabled"
                aria-label="Remove file"
              >
                <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                  />
                </svg>
              </button>
            </div>

            <button
              type="button"
              class="w-full mt-2 inline-flex justify-center items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              (click)="fileInput.click()"
              [disabled]="isDisabled"
            >
              {{
                field.fileConfig?.multiple ? 'Add more files' : 'Change file'
              }}
            </button>
          </div>
        </div>

        <!-- Upload Progress -->
        <div
          *ngIf="uploadProgress > 0 && uploadProgress < 100"
          class="w-full bg-gray-200 rounded-full h-2"
        >
          <div
            class="bg-blue-600 h-2 rounded-full transition-all duration-300"
            [style.width.%]="uploadProgress"
          ></div>
        </div>
      </div>
    </lib-field-wrapper>
  `,
})
export class FileFieldComponent implements OnInit {
  @Input() public field!: IField;
  @Input() public form!: FormGroup;

  @ViewChild('fileInput', { static: true })
  private readonly fileInput!: ElementRef<HTMLInputElement>;

  public selectedFiles: File[] = [];
  public isDisabled: boolean = false;
  public hasError: boolean = false;
  public isDragOver: boolean = false;
  public uploadProgress: number = 0;
  public acceptTypes: string = '';

  public constructor(private readonly formContext: FormContextService) {}

  public ngOnInit(): void {
    this.updateState();
    this.formContext.subscribe(() => this.updateState());
  }

  public onFileSelect(event: Event): void {
    const input = event.target as HTMLInputElement;

    if (input.files && input.files.length > 0)
      this.handleFiles(Array.from(input.files));
    else this.resetFileInput();
  }

  public onDragOver(event: DragEvent): void {
    event.preventDefault();
    this.isDragOver = true;
  }

  public onDragLeave(event: DragEvent): void {
    event.preventDefault();
    this.isDragOver = false;
  }

  public onDrop(event: DragEvent): void {
    event.preventDefault();
    this.isDragOver = false;

    if (event.dataTransfer?.files) {
      this.handleFiles(Array.from(event.dataTransfer.files));
    }
  }

  public removeFile(fileToRemove: File): void {
    this.selectedFiles = this.selectedFiles.filter(
      (file) => file !== fileToRemove
    );
    this.updateFormValue();
  }

  public trackFile(index: number, file: File): string {
    return file.name + file.size + file.lastModified;
  }

  private handleFiles(files: File[]): void {
    const validFiles = files.filter((file) => this.validateFile(file));

    if (this.field.fileConfig?.multiple) {
      this.selectedFiles = [...this.selectedFiles, ...validFiles];
    } else {
      this.selectedFiles = validFiles.slice(0, 1);
    }

    this.updateFormValue();
    this.resetFileInput();
    this.formContext.validateField(this.field.name);
  }

  private validateFile(file: File): boolean {
    if (this.acceptTypes) {
      const acceptedTypes = this.acceptTypes.split(',').map((t) => t.trim());
      const isValidType = acceptedTypes.some((type) => {
        if (type.startsWith('.')) {
          return file.name.toLowerCase().endsWith(type.toLowerCase());
        }
        return new RegExp(type.replace('*', '.*')).test(file.type);
      });

      if (!isValidType) {
        this.formContext.setError(
          this.field.name,
          `File type not allowed. Accepted types: ${this.acceptTypes}`
        );
        return false;
      }
    }

    const maxSize = this.field.fileConfig?.maxSize;
    if (maxSize && file.size > maxSize) {
      this.formContext.setError(
        this.field.name,
        `File size exceeds ${fileSizeFormatter(maxSize)}`
      );
      return false;
    }

    return true;
  }

  private updateFormValue(): void {
    const value = this.field.fileConfig?.multiple
      ? this.selectedFiles
      : this.selectedFiles[0] || null;
    this.formContext.setValue(this.field.name, value);
  }

  private updateState(): void {
    this.updateAcceptTypes();

    const value = this.formContext.getValue(this.field.name) || [];
    this.selectedFiles = Array.isArray(value) ? value : [value];
    this.isDisabled =
      this.formContext.getFieldProperty(this.field.name, 'disabled') || false;
    this.hasError = !!this.formContext.getError(this.field.name);
  }

  private updateAcceptTypes(): void {
    const accept = this.field.fileConfig?.accept;
    this.acceptTypes = Array.isArray(accept) ? accept.join(',') : accept || '';
  }

  private resetFileInput(): void {
    if (this.fileInput?.nativeElement) this.fileInput.nativeElement.value = '';
  }
}
