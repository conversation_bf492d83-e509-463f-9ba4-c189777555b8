import {
  Component,
  Input,
  OnInit,
  ViewChildren,
  QueryList,
  ElementRef,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IField } from '../../../interfaces';
import { FormContextService } from '../../../services';

@Component({
  selector: 'lib-otp-field',
  template: `
    <lib-field-wrapper [field]="field" [form]="form">
      <div class="flex justify-center space-x-2">
        <input
          *ngFor="let digit of digits; let i = index"
          #otpInput
          type="text"
          [id]="field.id + '_' + i"
          [name]="field.name + '_' + i"
          [value]="digit"
          [disabled]="isDisabled"
          maxlength="1"
          class="w-12 h-12 text-center text-lg font-semibold border-2 border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
          [ngClass]="{
            'border-red-500 focus:ring-red-500 focus:border-red-500': hasError,
            'border-green-500 focus:ring-green-500 focus:border-green-500':
              !hasError && isComplete,
            'bg-gray-50': isDisabled
          }"
          (input)="onInput($event, i)"
          (keydown)="onKeyDown($event, i)"
          (paste)="onPaste($event, i)"
          (focus)="onFocus(i)"
          (blur)="onBlur()"
        />
      </div>

      <div *ngIf="field.showResend" class="text-center mt-4">
        <button
          type="button"
          class="text-sm text-blue-600 hover:text-blue-800 disabled:text-gray-400 disabled:cursor-not-allowed"
          [disabled]="resendCooldown > 0"
          (click)="onResendClick()"
        >
          {{
            resendCooldown > 0
              ? 'Resend in ' + resendCooldown + 's'
              : 'Resend Code'
          }}
        </button>
      </div>
    </lib-field-wrapper>
  `,
})
export class OTPFieldComponent implements OnInit {
  @Input() public field!: IField;
  @Input() public form!: FormGroup;
  @ViewChildren('otpInput') public otpInputs!: QueryList<
    ElementRef<HTMLInputElement>
  >;

  public digits: string[] = [];
  public isDisabled: boolean = false;
  public hasError: boolean = false;
  public resendCooldown: number = 0;
  private resendTimer?: number;

  public constructor(private readonly formContext: FormContextService) {}

  public get length(): number {
    return this.field.otpLength || 6;
  }

  public get isComplete(): boolean {
    return (
      this.digits.every((digit) => digit !== '') &&
      this.digits.join('').length === this.length
    );
  }

  public ngOnInit(): void {
    this.initializeDigits();
    this.updateState();
    this.formContext.subscribe(() => this.updateState());
  }

  public ngOnDestroy(): void {
    if (this.resendTimer) clearInterval(this.resendTimer);
  }

  public onInput(event: Event, index: number): void {
    const input = event.target as HTMLInputElement;
    const value = input.value;

    // Only allow digits
    if (!/^\d*$/.test(value)) {
      input.value = this.digits[index];
      return;
    }

    // Update digit
    // this.digits[index] = value;

    // Auto-focus next input
    if (value && index < this.length - 1) {
      this.focusInput(index + 1);
    }

    this.updateFormValue();

    // Validate when complete
    if (this.isComplete) {
      this.formContext.validateField(this.field.name);
    }
  }

  public onKeyDown(event: KeyboardEvent, index: number): void {
    const input = event.target as HTMLInputElement;

    // Handle backspace
    if (event.key === 'Backspace') {
      if (!input.value && index > 0) {
        // Move to previous input if current is empty
        this.focusInput(index - 1);
        this.digits[index - 1] = '';
        this.updateFormValue();
      } else {
        // Clear current input
        this.digits[index] = '';
        input.value = '';
        this.updateFormValue();
      }
      return;
    }

    // Handle arrow keys
    if (event.key === 'ArrowLeft' && index > 0) {
      this.focusInput(index - 1);
      return;
    }

    if (event.key === 'ArrowRight' && index < this.length - 1) {
      this.focusInput(index + 1);
      return;
    }

    // Only allow digits
    if (!/^\d$/.test(event.key) && !['Tab', 'Shift'].includes(event.key)) {
      event.preventDefault();
    }
  }

  public onPaste(event: ClipboardEvent, index: number): void {
    if (!this.field.allowPaste) {
      event.preventDefault();
      return;
    }

    event.preventDefault();
    const pastedData = event.clipboardData?.getData('text') || '';
    const digits = pastedData
      .replace(/\D/g, '')
      .split('')
      .slice(0, this.length);

    // Fill digits starting from current index
    for (let i = 0; i < digits.length && index + i < this.length; i++) {
      this.digits[index + i] = digits[i];
    }

    this.updateFormValue();
    this.updateInputs();

    // Focus last filled input or next empty
    const lastFilledIndex = Math.min(
      index + digits.length - 1,
      this.length - 1
    );
    this.focusInput(lastFilledIndex);

    if (this.isComplete) {
      this.formContext.validateField(this.field.name);
    }
  }

  public onFocus(index: number): void {
    // Select all text on focus for easier editing
    setTimeout(() => {
      this.otpInputs.toArray()[index]?.nativeElement.select();
    }, 0);
  }

  public onBlur(): void {
    this.formContext.validateField(this.field.name);
  }

  public onResendClick(): void {
    if (this.resendCooldown > 0) return;

    // Emit resend event
    if (this.field.onResend) {
      this.field.onResend();
    }

    // Start cooldown
    this.startResendCooldown();
  }

  public clear(): void {
    this.digits = new Array(this.length).fill('');
    this.updateFormValue();
    this.updateInputs();
    this.focusInput(0);
  }

  private initializeDigits(): void {
    this.digits = new Array(this.length).fill('');

    // Initialize from form value if exists
    const value = this.formContext.getValue(this.field.name);
    if (value && typeof value === 'string') {
      const existingDigits = value.split('').slice(0, this.length);
      for (let i = 0; i < existingDigits.length; i++) {
        this.digits[i] = existingDigits[i] || '';
      }
    }
  }

  private updateFormValue(): void {
    const value = this.digits.join('');
    this.formContext.setValue(this.field.name, value);
  }

  private updateInputs(): void {
    console.log(this.digits);

    // Update input values to match digits array
    setTimeout(() => {
      this.otpInputs.toArray().forEach((input, index) => {
        const currentValue = input.nativeElement.value;
        const digit = this.digits[index] || '';
        if (currentValue !== digit) {
          input.nativeElement.value = digit;
        }
      });
    }, 0);
  }

  private focusInput(index: number): void {
    setTimeout(() => {
      const inputs = this.otpInputs.toArray();
      if (inputs[index]) {
        inputs[index].nativeElement.focus();
      }
    }, 0);
  }

  private startResendCooldown(): void {
    this.resendCooldown = this.field.resendCooldown || 30;

    this.resendTimer = window.setInterval(() => {
      this.resendCooldown--;
      if (this.resendCooldown <= 0) {
        clearInterval(this.resendTimer);
      }
    }, 1000);
  }

  private updateState(): void {
    const value = this.formContext.getValue(this.field.name) || '';

    // Update digits from form value
    if (typeof value === 'string' && value !== this.digits.join('')) {
      const newDigits = value.split('').slice(0, this.length);
      for (let i = 0; i < this.length; i++) {
        this.digits[i] = newDigits[i] || '';
      }
      this.updateInputs();
    }

    this.isDisabled =
      this.formContext.getFieldProperty(this.field.name, 'disabled') || false;
    this.hasError = !!this.formContext.getError(this.field.name);
  }
}
