import { Component, Input, OnInit, ViewChild, ElementRef } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IField, IOption } from '../../../interfaces';
import { FormContextService } from '../../../services';

@Component({
  selector: 'lib-tags-field',
  template: `
    <lib-field-wrapper [field]="field" [form]="form">
      <div
        class="min-h-[42px] px-3 py-2 border border-gray-300 rounded-md bg-white focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 transition-all"
        [ngClass]="{
          'border-red-500 focus-within:ring-red-500 focus-within:border-red-500':
            hasError,
          'border-green-500 focus-within:ring-green-500 focus-within:border-green-500':
            !hasError && selectedTags.length > 0,
          'bg-gray-50': isDisabled
        }"
        (click)="focusInput()"
      >
        <!-- Selected Tags -->
        <div class="flex flex-wrap gap-1 items-center">
          <span
            *ngFor="let tag of selectedTags; trackBy: trackTag"
            class="inline-flex items-center px-2 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800 border border-blue-200"
          >
            {{ tag.label }}
            <button
              type="button"
              class="ml-1 w-4 h-4 rounded-full hover:bg-blue-200 flex items-center justify-center"
              [disabled]="isDisabled"
              (click)="removeTag(tag)"
            >
              <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fill-rule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>
          </span>

          <!-- Input -->
          <input
            #tagInput
            type="text"
            [value]="inputValue"
            [placeholder]="
              selectedTags.length === 0
                ? field.placeholder || 'Add tags...'
                : ''
            "
            [disabled]="
              isDisabled ||
              (field.maxTags && selectedTags.length >= field.maxTags)
            "
            class="flex-1 min-w-[120px] border-none outline-none bg-transparent"
            (input)="onInput($event)"
            (keydown)="onKeyDown($event)"
            (blur)="onBlur()"
            (focus)="onFocus()"
          />
        </div>
      </div>

      <!-- Suggestions Dropdown -->
      <div
        *ngIf="showSuggestions && filteredSuggestions.length > 0"
        class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-48 overflow-y-auto"
      >
        <button
          *ngFor="let suggestion of filteredSuggestions; let i = index"
          type="button"
          class="w-full px-3 py-2 text-left hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
          [ngClass]="{ 'bg-blue-50': i === highlightedIndex }"
          (click)="selectSuggestion(suggestion)"
          (mouseenter)="highlightedIndex = i"
        >
          {{ suggestion.label }}
        </button>
      </div>
    </lib-field-wrapper>
  `,
})
export class TagsFieldComponent implements OnInit {
  @Input() public field!: IField;
  @Input() public form!: FormGroup;
  @ViewChild('tagInput') public tagInput!: ElementRef<HTMLInputElement>;

  public selectedTags: IOption[] = [];
  public inputValue: string = '';
  public isDisabled: boolean = false;
  public hasError: boolean = false;
  public showSuggestions: boolean = false;
  public highlightedIndex: number = -1;
  public suggestions: IOption[] = [];
  public filteredSuggestions: IOption[] = [];

  public constructor(private readonly formContext: FormContextService) {}

  public ngOnInit(): void {
    this.loadSuggestions();
    this.updateState();
    this.formContext.subscribe(() => this.updateState());
  }

  public onInput(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.inputValue = value;
    this.filterSuggestions(value);
    this.showSuggestions = value.length >= (this.field.minChars || 1);
    this.highlightedIndex = -1;
  }

  public onKeyDown(event: KeyboardEvent): void {
    switch (event.key) {
      case 'Enter':
        event.preventDefault();
        if (
          this.highlightedIndex >= 0 &&
          this.filteredSuggestions[this.highlightedIndex]
        ) {
          this.selectSuggestion(
            this.filteredSuggestions[this.highlightedIndex]
          );
        } else if (this.inputValue.trim() && this.field.allowCustom) {
          this.addCustomTag(this.inputValue.trim());
        }
        break;

      case 'ArrowDown':
        event.preventDefault();
        this.highlightedIndex = Math.min(
          this.highlightedIndex + 1,
          this.filteredSuggestions.length - 1
        );
        break;

      case 'ArrowUp':
        event.preventDefault();
        this.highlightedIndex = Math.max(this.highlightedIndex - 1, -1);
        break;

      case 'Escape':
        this.showSuggestions = false;
        this.highlightedIndex = -1;
        break;

      case 'Backspace':
        if (!this.inputValue && this.selectedTags.length > 0) {
          this.removeTag(this.selectedTags[this.selectedTags.length - 1]);
        }
        break;

      case ',':
      case ';':
        if (this.field.separators?.includes(event.key)) {
          event.preventDefault();
          if (this.inputValue.trim() && this.field.allowCustom) {
            this.addCustomTag(this.inputValue.trim());
          }
        }
        break;
    }
  }

  public onFocus(): void {
    if (this.inputValue.length >= (this.field.minChars || 1)) {
      this.showSuggestions = true;
    }
  }

  public onBlur(): void {
    // Delay hiding suggestions to allow click selection
    setTimeout(() => {
      this.showSuggestions = false;
      this.formContext.validateField(this.field.name);
    }, 150);
  }

  public selectSuggestion(suggestion: IOption): void {
    if (this.isTagSelected(suggestion)) return;
    if (this.field.maxTags && this.selectedTags.length >= this.field.maxTags)
      return;

    this.selectedTags.push(suggestion);
    this.inputValue = '';
    this.showSuggestions = false;
    this.highlightedIndex = -1;
    this.updateFormValue();
    this.focusInput();
    this.formContext.validateField(this.field.name);
  }

  public addCustomTag(value: string): void {
    if (!this.field.allowCustom) return;
    if (this.field.maxTags && this.selectedTags.length >= this.field.maxTags)
      return;

    const customTag: IOption = { value, label: value };
    if (this.isTagSelected(customTag)) return;

    this.selectedTags.push(customTag);
    this.inputValue = '';
    this.showSuggestions = false;
    this.updateFormValue();
    this.focusInput();
    this.formContext.validateField(this.field.name);
  }

  public removeTag(tagToRemove: IOption): void {
    this.selectedTags = this.selectedTags.filter(
      (tag) => tag.value !== tagToRemove.value
    );
    this.updateFormValue();
    this.focusInput();
    this.formContext.validateField(this.field.name);
  }

  public focusInput(): void {
    setTimeout(() => {
      this.tagInput?.nativeElement.focus();
    }, 0);
  }

  public trackTag(index: number, tag: IOption): any {
    return tag.value;
  }

  private isTagSelected(tag: IOption): boolean {
    return this.selectedTags.some((selected) => selected.value === tag.value);
  }

  private filterSuggestions(query: string): void {
    if (!query.trim()) {
      this.filteredSuggestions = [];
      return;
    }

    const lowerQuery = query.toLowerCase();
    this.filteredSuggestions = this.suggestions
      .filter(
        (suggestion) =>
          !this.isTagSelected(suggestion) &&
          suggestion.label.toLowerCase().includes(lowerQuery)
      )
      .slice(0, this.field.maxSuggestions || 10);
  }

  private loadSuggestions(): void {
    if (Array.isArray(this.field.suggestions)) {
      this.suggestions = this.field.suggestions.map((item) =>
        typeof item === 'string' ? { value: item, label: item } : item
      );
    } else if (this.field.options) {
      if (Array.isArray(this.field.options)) {
        this.suggestions = this.field.options;
      } else if (this.field.options.data) {
        this.suggestions = this.field.options.data;
      }
    }
  }

  private updateFormValue(): void {
    const values = this.selectedTags.map((tag) => tag.value);
    this.formContext.setValue(this.field.name, values);
  }

  private updateState(): void {
    const values = this.formContext.getValue(this.field.name) || [];

    if (Array.isArray(values)) {
      this.selectedTags = values.map((value) => {
        // Try to find in suggestions first
        const existingTag = this.suggestions.find((s) => s.value === value);
        return existingTag || { value, label: value };
      });
    }

    this.isDisabled =
      this.formContext.getFieldProperty(this.field.name, 'disabled') || false;
    this.hasError = !!this.formContext.getError(this.field.name);
  }
}
