import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IField } from '../../../interfaces';
import { FormContextService } from '../../../services';

@Component({
  selector: 'lib-textarea-field',
  template: `
    <lib-field-wrapper [field]="field" [form]="form">
      <div class="relative ">
        <textarea
          [id]="field.id"
          [name]="field.name"
          [value]="value"
          [placeholder]="field.placeholder ?? ''"
          [readonly]="field.readonly"
          [disabled]="isDisabled"
          [attr.minlength]="field.minLength ?? null"
          [attr.maxlength]="field.maxLength ?? null"
          [attr.rows]="field.rows ?? 4"
          [attr.cols]="field.cols ?? null"
          class="block w-full px-3 py-2 text-base text-gray-900 bg-white border border-gray-300 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:opacity-100 read-only:bg-gray-50 resize-y"
          [ngClass]="[
            field.className || '',
            hasError
              ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
              : '',
            !hasError && value
              ? 'border-green-500 focus:ring-green-500 focus:border-green-500'
              : ''
          ]"
          [ngStyle]="field.style || {}"
          (input)="onInput($event)"
          (blur)="onBlur()"
          (focus)="onFocus()"
        ></textarea>

        <div
          *ngIf="showCharacterCount"
          class="absolute top-full mt-1 end-0 text-xs text-gray-500 pointer-events-none select-none"
        >
          {{ characterCount }}{{ field.maxLength ? '/' + field.maxLength : '' }}
        </div>
      </div>
    </lib-field-wrapper>
  `,
})
export class TextareaFieldComponent implements OnInit {
  @Input() public field!: IField;
  @Input() public form!: FormGroup;

  public value: string = '';
  public isDisabled: boolean = false;
  public hasError: boolean = false;
  public characterCount: number = 0;
  public showCharacterCount: boolean = false;

  public constructor(private readonly formContext: FormContextService) {}

  public ngOnInit(): void {
    this.showCharacterCount =
      !!this.field.maxLength || this.field.showCharacterCount === true;

    this.updateState();
    this.formContext.subscribe(() => this.updateState());
  }

  public onInput(event: Event): void {
    const value = (event.target as HTMLTextAreaElement).value;
    this.characterCount = value.length;
    this.formContext.setValue(this.field.name, value);
  }

  public onBlur(): void {
    this.formContext.validateField(this.field.name);
  }

  public onFocus(): void {
    // Handle focus events if needed
  }

  private updateState(): void {
    this.value = this.formContext.getValue(this.field.name) || '';
    this.characterCount = this.value.length;
    this.isDisabled =
      this.formContext.getFieldProperty(this.field.name, 'disabled') || false;
    this.hasError = !!this.formContext.getError(this.field.name);
  }
}
